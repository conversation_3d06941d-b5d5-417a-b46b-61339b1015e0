# SnapBack License Management Dashboard Specification

## Overview

This specification outlines a comprehensive dashboard for the SnapBack license management system, designed for Mexican market compliance with SAT (Servicio de Administración Tributaria) requirements, multi-currency support (USD/MXN), and complete business operations management.

## Technical Stack Integration

- **Frontend**: Next.js 15, React 18, TypeScript, shadcn/ui components
- **Backend**: Express.js API (documented in `apps/server/API_DOCUMENTATION.md`)
- **Database**: PostgreSQL with Prisma ORM
- **Payment Processing**: Stripe with webhook integration
- **Styling**: Tailwind CSS with shadcn/ui component library
- **State Management**: React Query (TanStack Query) for server state

## Dashboard Architecture

### Navigation Structure
```
├── Overview (Dashboard Home)
├── License Management
│   ├── Active Licenses
│   ├── License Creation
│   └── License Analytics
├── Customer Management
│   ├── Customer Directory
│   ├── Customer Communications
│   └── Tax Information (RFC)
├── Payment & Billing
│   ├── Payment Tracking
│   ├── Refund Management
│   └── Revenue Analytics
├── Tax Compliance (Mexico)
│   ├── Monthly Reports
│   ├── Invoice Generation
│   ├── SAT Export Tools
│   └── Tax Rate Management
├── Analytics & Reports
│   ├── Business Metrics
│   ├── Revenue Trends
│   └── Customer Insights
└── System Administration
    ├── System Health
    ├── API Monitoring
    └── User Management
```

## Detailed View Specifications

## 1. Overview Dashboard

### Purpose
Central command center providing real-time business metrics and quick access to critical functions.

### UI Components
- **Stats Cards Grid** (4 columns on desktop, 2 on tablet, 1 on mobile)
  - Total Revenue (USD/MXN toggle)
  - Active Licenses Count
  - Total Customers
  - Monthly Growth Rate
  - Pending Refunds Alert
  - System Health Status

- **Revenue Chart** (Area Chart)
  - Time range selector (7D, 30D, 90D, 1Y)
  - Currency toggle (USD/MXN)
  - Comparison with previous period

- **Recent Activity Feed** (Card with scrollable list)
  - License creations
  - Payment completions
  - Refund requests
  - System alerts

- **Quick Actions Panel**
  - Create License (Modal trigger)
  - Process Refund (Navigation)
  - Generate Tax Report (Modal trigger)
  - Export Data (Dropdown menu)

### Data Sources
- **API Endpoints**:
  - Custom analytics endpoint (to be created): `GET /api/analytics/overview`
  - `GET /api/licenses/status/{licenseKey}` (for license counts)
  - `GET /api/refunds/history` (for refund metrics)
  - Custom revenue endpoint: `GET /api/analytics/revenue`

### Real-time Updates
- WebSocket connection for live license creation notifications
- Polling every 30 seconds for revenue updates
- Real-time refund status changes

### Responsive Design
- Mobile: Single column layout with collapsible sections
- Tablet: 2-column grid with adjusted chart sizes
- Desktop: Full 4-column grid with expanded charts

## 2. License Management

### 2.1 Active Licenses View

### Purpose
Comprehensive license management with search, filtering, and bulk operations.

### UI Components
- **Search & Filter Bar**
  - Search input (license key, email, customer name)
  - License type filter (Trial, Standard, Extended)
  - Status filter (Active, Expired, Refunded)
  - Date range picker
  - Export button (CSV, PDF)

- **Licenses Data Table** (shadcn/ui Table component)
  - Columns:
    - License Key (monospace font, copyable)
    - Customer Email (clickable to customer profile)
    - License Type (Badge component)
    - Status (Badge with color coding)
    - Devices Used/Max (Progress indicator)
    - Created Date (formatted)
    - Revenue (USD/MXN)
    - Actions (Dropdown menu)
  - Pagination (50 items per page)
  - Sortable columns
  - Row selection for bulk operations

- **License Details Modal**
  - License information
  - Device list with metadata
  - Payment history
  - Refund status
  - Customer tax information (RFC)

- **Bulk Actions Toolbar** (appears when rows selected)
  - Send license emails
  - Export selected
  - Generate invoices (Mexican format)

### Data Sources
- **API Endpoints**:
  - `GET /api/licenses/status/{licenseKey}` (for individual license details)
  - Custom endpoint: `GET /api/licenses/list` (paginated, filtered)
  - `GET /api/licenses/devices/{deviceId}` (for device information)

### User Actions
- **View**: Click row to open details modal
- **Edit**: Inline editing for customer information
- **Resend**: `POST /api/licenses/resend`
- **Upgrade**: `POST /api/licenses/upgrade`
- **Refund**: Navigate to refund processing
- **Export**: Generate CSV/PDF reports

### Search & Filtering
- Real-time search with debouncing (300ms)
- Multiple filter combinations
- Saved filter presets
- URL state persistence

### Pagination
- Server-side pagination
- Page size options: 25, 50, 100
- Jump to page input
- Total count display

## 2.2 License Creation

### Purpose
Streamlined license creation process with payment integration and tax compliance.

### UI Components
- **License Creation Form** (Multi-step wizard)
  - Step 1: Customer Information
    - Email (required)
    - Name (required)
    - RFC Number (required for Mexican customers)
    - Tax Address (required for invoicing)
    - Phone (optional)
  
  - Step 2: License Configuration
    - License Type selection (Radio buttons with pricing)
    - Additional devices (Number input with cost calculation)
    - Currency selection (USD/MXN)
    - Tax rate application (automatic based on customer location)
  
  - Step 3: Payment Processing
    - Payment method selection
    - Stripe Elements integration
    - Tax calculation display
    - Total amount confirmation

- **Pricing Calculator** (Real-time updates)
  - Base license cost
  - Additional device costs
  - Tax calculations (IVA for Mexican customers)
  - Currency conversion rates
  - Total in selected currency

### Data Sources
- **API Endpoints**:
  - `GET /api/payments/pricing` (current pricing)
  - `POST /api/licenses/create` (license creation)
  - `POST /api/payments/create-payment-intent` (payment processing)
  - Custom endpoint: `GET /api/tax/rates` (tax rate information)

### User Actions
- **Create Trial**: Direct license creation without payment
- **Create Paid**: Full payment flow with Stripe integration
- **Save Draft**: Store incomplete forms
- **Duplicate**: Copy settings from existing license

### Validation
- Real-time form validation
- Email uniqueness check
- RFC format validation for Mexican customers
- Payment amount verification

## 3. Customer Management

### 3.1 Customer Directory

### Purpose
Centralized customer database with tax compliance information and communication history.

### UI Components
- **Customer Search & Filter**
  - Name/email search
  - Country filter
  - License type filter
  - Registration date range
  - Tax status filter (RFC provided/missing)

- **Customer Data Table**
  - Columns:
    - Name (sortable)
    - Email (clickable)
    - Country (Flag icon + name)
    - Total Licenses (clickable count)
    - Total Revenue (USD/MXN)
    - RFC Status (Badge for Mexican customers)
    - Last Activity (relative time)
    - Actions (Dropdown)

- **Customer Profile Modal**
  - Personal Information
  - Tax Information (RFC, address)
  - License History
  - Payment History
  - Communication Log
  - Support Tickets

### Data Sources
- **API Endpoints**:
  - Custom endpoint: `GET /api/customers/list` (to be created)
  - Custom endpoint: `GET /api/customers/{customerId}` (profile details)
  - `GET /api/licenses/status/{licenseKey}` (customer licenses)

### User Actions
- **View Profile**: Open customer details modal
- **Edit Information**: Update customer data
- **Send Email**: Communication tools
- **Generate Invoice**: Mexican tax-compliant invoices
- **Export Data**: Customer data export for compliance

## 4. Payment & Billing

### 4.1 Payment Tracking

### Purpose
Monitor all payment transactions with multi-currency support and tax tracking.

### UI Components
- **Payment Dashboard**
  - Revenue metrics cards
  - Payment status distribution (Chart)
  - Currency breakdown (USD/MXN)
  - Tax collected summary

- **Payments Data Table**
  - Columns:
    - Payment ID (Stripe link)
    - Customer Email
    - Amount (Original currency + converted)
    - Tax Amount (IVA for Mexican customers)
    - Status (Badge with colors)
    - Payment Method
    - Date (sortable)
    - Actions

- **Payment Details Modal**
  - Transaction information
  - Customer details
  - Tax breakdown
  - Refund history
  - Related licenses

### Data Sources
- **API Endpoints**:
  - `GET /api/payments/checkout-session/{sessionId}`
  - `GET /api/payments/payment-intent/{paymentIntentId}`
  - Custom endpoint: `GET /api/payments/list` (paginated payments)

### User Actions
- **View Details**: Open payment information
- **Process Refund**: Initiate refund workflow
- **Generate Receipt**: Create customer receipt
- **Export Transactions**: Financial reporting

## 5. Tax Compliance (Mexico)

### 5.1 Monthly Tax Reports

### Purpose
Generate SAT-compliant reports for Mexican tax obligations.

### UI Components
- **Report Generation Form**
  - Month/Year selector
  - Report type selection (Revenue, IVA, Customer)
  - Currency options
  - Export format (PDF, XML, CSV)

- **Tax Summary Cards**
  - Total Revenue (MXN)
  - IVA Collected
  - Number of Transactions
  - Customer Count (with RFC)

- **Tax Report Table**
  - Transaction-level details
  - Customer RFC information
  - Tax calculations
  - Compliance status

### Data Sources
- **API Endpoints**:
  - Custom endpoint: `GET /api/tax/reports/monthly` (to be created)
  - Custom endpoint: `GET /api/tax/summary/{period}` (tax summaries)

### User Actions
- **Generate Report**: Create tax reports
- **Export SAT Format**: XML export for SAT submission
- **Validate Data**: Check compliance requirements
- **Archive Reports**: Store historical reports

### Mexican Tax Compliance Features
- **RFC Validation**: Verify customer tax IDs
- **IVA Calculation**: Automatic tax rate application
- **Invoice Generation**: Mexican tax-compliant invoices
- **SAT Export**: XML format for tax authority submission
- **Currency Conversion**: USD to MXN with official rates

## Technical Implementation Notes

### State Management
- React Query for server state management
- Zustand for client-side state (filters, UI preferences)
- Form state with React Hook Form + Zod validation

### Real-time Features
- WebSocket connections for live updates
- Server-sent events for payment status changes
- Optimistic updates for better UX

### Performance Optimization
- Virtual scrolling for large data tables
- Lazy loading for modal content
- Debounced search inputs
- Cached API responses with React Query

### Security Considerations
- Role-based access control
- Audit logging for sensitive operations
- Data encryption for tax information
- Secure API endpoints with rate limiting

### Mobile Responsiveness
- Progressive Web App (PWA) capabilities
- Touch-friendly interface elements
- Responsive data tables with horizontal scrolling
- Mobile-optimized forms and modals

## 6. Analytics & Reports

### 6.1 Business Metrics Dashboard

### Purpose
Comprehensive business intelligence with KPI tracking and trend analysis.

### UI Components
- **KPI Metrics Grid**
  - Monthly Recurring Revenue (MRR)
  - Customer Acquisition Cost (CAC)
  - Customer Lifetime Value (CLV)
  - Churn Rate
  - Average Revenue Per User (ARPU)
  - License Conversion Rate (Trial to Paid)

- **Revenue Analytics Charts**
  - Revenue trend (Line chart with period comparison)
  - Revenue by license type (Pie chart)
  - Geographic revenue distribution (Map visualization)
  - Monthly growth rate (Bar chart)

- **Customer Analytics**
  - Customer acquisition funnel
  - Customer segmentation analysis
  - Retention cohort analysis
  - Support ticket trends

### Data Sources
- **API Endpoints**:
  - Custom endpoint: `GET /api/analytics/kpis` (key performance indicators)
  - Custom endpoint: `GET /api/analytics/revenue-trends`
  - Custom endpoint: `GET /api/analytics/customer-metrics`
  - Custom endpoint: `GET /api/analytics/geographic-data`

### User Actions
- **Export Reports**: PDF/Excel export with charts
- **Schedule Reports**: Automated email reports
- **Custom Date Ranges**: Flexible period selection
- **Drill Down**: Click charts for detailed views

## 7. System Administration

### 7.1 System Health Monitoring

### Purpose
Monitor system performance, API health, and operational metrics.

### UI Components
- **System Status Cards**
  - API Response Time
  - Database Connection Status
  - Stripe Integration Health
  - Email Service Status
  - Background Job Queue

- **Performance Metrics**
  - Request volume (Real-time chart)
  - Error rate tracking
  - Database query performance
  - Memory and CPU usage

- **Alert Management**
  - Active alerts list
  - Alert history
  - Notification settings
  - Escalation rules

### Data Sources
- **API Endpoints**:
  - Custom endpoint: `GET /api/system/health`
  - Custom endpoint: `GET /api/system/metrics`
  - Custom endpoint: `GET /api/system/alerts`

### User Actions
- **Acknowledge Alerts**: Mark alerts as seen
- **Configure Monitoring**: Set thresholds and rules
- **Export Logs**: System log downloads
- **Restart Services**: Emergency system controls

## Implementation Roadmap

### Phase 1: Core Dashboard (Weeks 1-4)
1. **Overview Dashboard**
   - Basic stats cards
   - Revenue chart
   - Recent activity feed
   - Navigation structure

2. **License Management**
   - License listing with search/filter
   - License details modal
   - Basic CRUD operations

3. **Customer Management**
   - Customer directory
   - Basic customer profiles
   - Search and filtering

### Phase 2: Payment & Tax Compliance (Weeks 5-8)
1. **Payment Tracking**
   - Payment transaction listing
   - Payment details and history
   - Multi-currency support

2. **Mexican Tax Compliance**
   - RFC validation and storage
   - IVA calculation system
   - Basic invoice generation
   - Monthly report generation

3. **Refund Management**
   - Refund request processing
   - Refund status tracking
   - Integration with Stripe refunds

### Phase 3: Advanced Features (Weeks 9-12)
1. **Analytics & Reporting**
   - Business metrics dashboard
   - Advanced charts and visualizations
   - Custom report generation
   - Export capabilities

2. **System Administration**
   - Health monitoring dashboard
   - Alert management system
   - User role management
   - Audit logging

3. **Performance Optimization**
   - Virtual scrolling for large datasets
   - Advanced caching strategies
   - Real-time updates optimization

## API Extensions Required

### New Endpoints to Implement

```typescript
// Analytics Endpoints
GET /api/analytics/overview
GET /api/analytics/revenue-trends
GET /api/analytics/customer-metrics
GET /api/analytics/kpis

// Customer Management
GET /api/customers/list
GET /api/customers/{customerId}
PUT /api/customers/{customerId}

// Tax Compliance
GET /api/tax/rates
GET /api/tax/reports/monthly
POST /api/tax/invoices/generate

// System Monitoring
GET /api/system/health
GET /api/system/metrics
GET /api/system/alerts

// Enhanced License Management
GET /api/licenses/list (paginated with filters)
GET /api/licenses/analytics
```

### Database Schema Extensions

```sql
-- Customer tax information
ALTER TABLE customers ADD COLUMN rfc VARCHAR(13);
ALTER TABLE customers ADD COLUMN tax_address JSONB;
ALTER TABLE customers ADD COLUMN country_code VARCHAR(2);

-- Tax tracking
CREATE TABLE tax_transactions (
  id UUID PRIMARY KEY,
  license_id UUID REFERENCES licenses(id),
  tax_rate DECIMAL(5,4),
  tax_amount INTEGER,
  currency VARCHAR(3),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Invoice generation
CREATE TABLE invoices (
  id UUID PRIMARY KEY,
  customer_id UUID,
  license_id UUID REFERENCES licenses(id),
  invoice_number VARCHAR(50) UNIQUE,
  amount INTEGER,
  tax_amount INTEGER,
  currency VARCHAR(3),
  status VARCHAR(20),
  generated_at TIMESTAMP DEFAULT NOW()
);
```

## Security & Compliance Considerations

### Data Protection
- **GDPR Compliance**: Customer data export/deletion capabilities
- **Mexican Data Protection**: LFPDPPP compliance measures
- **PCI DSS**: Secure payment data handling
- **Encryption**: At-rest and in-transit data encryption

### Access Control
- **Role-Based Access**: Admin, Manager, Viewer roles
- **Audit Logging**: All administrative actions logged
- **Session Management**: Secure authentication with JWT
- **API Rate Limiting**: Prevent abuse and ensure availability

### Tax Compliance
- **SAT Requirements**: Mexican tax authority compliance
- **Invoice Numbering**: Sequential invoice number generation
- **Digital Signatures**: Electronic invoice validation
- **Data Retention**: 5-year retention for tax records

This comprehensive specification provides a complete roadmap for implementing a world-class license management dashboard with full Mexican tax compliance and modern web application best practices.
