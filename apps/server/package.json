{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:start": "docker compose up -d", "db:watch": "docker compose up", "db:stop": "docker compose stop", "db:down": "docker compose down", "stripe:listen": "stripe listen --forward-to http://localhost:3000/api/stripe/webhook"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@snapback/shared": "workspace:*", "bcryptjs": "^3.0.2", "crypto": "^1.0.1", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.5", "stripe": "^18.4.0", "dotenv": "^17.2.1", "zod": "^4.0.2", "express": "^5.1.0", "cors": "^2.8.5", "@prisma/client": "^6.13.0", "better-auth": "^1.3.4"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "@vitest/ui": "^3.2.4", "supertest": "^7.1.4", "vitest": "^3.2.4", "tsdown": "^0.12.9", "typescript": "^5.8.2", "@types/express": "^5.0.1", "@types/cors": "^2.8.17", "tsx": "^4.19.2", "@types/node": "^22.13.11", "prisma": "^6.13.0"}}