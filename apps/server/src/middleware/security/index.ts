// security.ts - Advanced Security Middleware

import crypto from "crypto";
import type { NextFunction, Request, Response } from "express";
import rateLimit from "express-rate-limit";
import type { Device } from "prisma/generated/client";
import prisma from "../../../prisma";

// Type definitions
interface SecurityContext {
	ipAddress: string | undefined;
	userAgent: string | undefined;
	suspiciousScore: number;
}

interface RateLimitConfig {
	windowMs: number;
	max: number;
	message: string;
}

interface AuditLogDevice {
	deviceHash: string | null;
}

// Extend Express Request interface
declare global {
	namespace Express {
		interface Request {
			security?: SecurityContext;
			deviceFingerprint?: string;
		}
	}
}

// Anti-spoofing middleware
export const antiSpoofingMiddleware = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const { deviceId, licenseKey } = req.body;
		const userAgent = req.get("User-Agent");
		const ipAddress = req.ip || req.socket?.remoteAddress;

		// Check for suspicious patterns
		const suspiciousIndicators = [
			// Empty or generic user agents
			!userAgent || userAgent.includes("curl") || userAgent.includes("wget"),

			// Rapid requests from same IP for different device IDs
			await checkRapidDeviceIdChanges(ipAddress),

			// Device ID format validation
			deviceId && !isValidDeviceIdFormat(deviceId),

			// Check for known malicious patterns
			await checkMaliciousPatterns(deviceId, userAgent, ipAddress),
		];

		const suspiciousScore = suspiciousIndicators.filter(Boolean).length;

		if (suspiciousScore >= 2) {
			// Log suspicious activity
			await prisma.auditLog.create({
				data: {
					action: "SUSPICIOUS_ACTIVITY",
					licenseKey: licenseKey || null,
					deviceHash: deviceId
						? crypto.createHash("sha256").update(deviceId).digest("hex")
						: null,
					ipAddress,
					userAgent,
					details: {
						suspiciousScore,
						indicators: suspiciousIndicators
							.map((indicator, index) => ({
								type: [
									"generic_user_agent",
									"rapid_device_changes",
									"invalid_device_format",
									"malicious_patterns",
								][index],
								triggered: indicator,
							}))
							.filter((i) => i.triggered),
					},
				},
			});

			return res.status(429).json({
				error: "Request blocked due to suspicious activity",
				retryAfter: 3600, // 1 hour
			});
		}

		// Add security context to request
		req.security = {
			ipAddress,
			userAgent,
			suspiciousScore,
		};

		next();
	} catch (error) {
		console.error("Security middleware error:", error);
		next();
	}
};

// Check for rapid device ID changes from same IP
async function checkRapidDeviceIdChanges(
	ipAddress: string | undefined,
): Promise<boolean> {
	if (!ipAddress) return false;

	const timeWindow = new Date(Date.now() - 15 * 60 * 1000); // 15 minutes

	const recentLogs = await prisma.auditLog.findMany({
		where: {
			ipAddress,
			createdAt: { gte: timeWindow },
			action: { in: ["LICENSE_VALIDATED", "DEVICE_ADDED"] },
		},
		select: {
			deviceHash: true,
		},
	});

	const uniqueDevices = new Set(
		recentLogs.map((log: AuditLogDevice) => log.deviceHash),
	);
	return uniqueDevices.size > 3; // More than 3 different devices in 15 minutes
}

// Validate device ID format - accepts both hex strings and UUID formats
function isValidDeviceIdFormat(deviceId: string): boolean {
	// Accept pure hexadecimal strings (SHA256 format: 64 chars, or other reasonable lengths)
	const hexPattern = /^[a-fA-F0-9]{32,128}$/;
	// Accept UUID format (with hyphens: 36 chars)
	const uuidPattern =
		/^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/i;

	return hexPattern.test(deviceId) || uuidPattern.test(deviceId);
}

// Check for known malicious patterns
async function checkMaliciousPatterns(
	deviceId: string | undefined,
	userAgent: string | undefined,
	ipAddress: string | undefined,
): Promise<boolean> {
	const maliciousPatterns = [
		// Known bot user agents
		/bot|crawler|spider|scraper/i.test(userAgent || ""),

		// Sequential device IDs (indicating generation)
		deviceId && (await isSequentialDeviceId(deviceId)),

		// IP in blocklist
		await isBlockedIP(ipAddress),
	];

	return maliciousPatterns.some(Boolean);
}

// Check for sequential device IDs
async function isSequentialDeviceId(deviceId: string): Promise<boolean> {
	// Check if this device ID is part of a sequence
	const currentDeviceNum = Number.parseInt(deviceId.slice(-8), 16);
	if (Number.isNaN(currentDeviceNum)) return false;

	const timeWindow = new Date(Date.now() - 60 * 60 * 1000); // 1 hour

	const recentDevices = await prisma.auditLog.findMany({
		where: {
			createdAt: { gte: timeWindow },
			deviceHash: { not: null },
		},
		select: { deviceHash: true },
		take: 50,
	});

	// Check if we have sequential numbers in the last 8 hex digits
	const recentNums = recentDevices
		.map((log: AuditLogDevice) => {
			if (!log.deviceHash) return Number.NaN;
			return Number.parseInt(log.deviceHash.slice(-8), 16);
		})
		.filter((num: number) => !Number.isNaN(num))
		.sort((a: number, b: number) => a - b);

	// Check if current device ID is sequential with any recent device
	for (const num of recentNums) {
		if (Math.abs(currentDeviceNum - num) === 1) {
			return true; // Sequential numbers found
		}
	}

	return false;
}

// Check if IP is in blocklist
async function isBlockedIP(ipAddress: string | undefined): Promise<boolean> {
	if (!ipAddress) return false;

	// Implement your IP blocklist logic here
	// This could check against a database of known malicious IPs
	const blockedIPs = process.env.BLOCKED_IPS?.split(",") || [];
	return blockedIPs.includes(ipAddress);
}

// Enhanced rate limiting with sliding window
export const createAdvancedRateLimit = (config: RateLimitConfig) => {
	return rateLimit({
		windowMs: config.windowMs,
		max: config.max,
		message: config.message,

		// Custom key generator that includes device fingerprinting
		keyGenerator: (req) => {
			const baseKey = req.ip;
			const deviceId = req.body?.deviceId;
			const userAgent = req.get("User-Agent");

			// Create a more sophisticated key that considers multiple factors
			const fingerprint = crypto
				.createHash("sha256")
				.update(`${baseKey}-${deviceId || ""}-${userAgent || ""}`)
				.digest("hex")
				.slice(0, 16);

			return `${baseKey}-${fingerprint}`;
		},

		// Skip successful requests for license validation to allow legitimate usage
		skip: (req, res) => {
			return req.path === "/api/licenses/validate" && res.statusCode === 200;
		},

		// Custom handler for rate limit exceeded
		handler: async (req, res) => {
			const ipAddress = req.ip;
			const userAgent = req.get("User-Agent");

			// Log rate limit violation
			await prisma.auditLog.create({
				data: {
					action: "RATE_LIMIT_EXCEEDED",
					ipAddress,
					userAgent,
					details: {
						endpoint: req.path,
						method: req.method,
					},
				},
			});

			res.status(429).json({
				error: config.message,
				retryAfter: Math.ceil(config.windowMs / 1000),
			});
		},
	});
};

// Device fingerprinting middleware
export const deviceFingerprintMiddleware = (
	req: Request,
	_res: Response,
	next: NextFunction,
) => {
	const userAgent = req.get("User-Agent") || "";
	const acceptLanguage = req.get("Accept-Language") || "";
	const acceptEncoding = req.get("Accept-Encoding") || "";

	// Create a device fingerprint from headers
	const fingerprint = crypto
		.createHash("sha256")
		.update(`${userAgent}${acceptLanguage}${acceptEncoding}`)
		.digest("hex");

	req.deviceFingerprint = fingerprint;
	next();
};

// License validation enhancement with additional security checks
export const enhancedLicenseValidation = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const { licenseKey, deviceId } = req.body;

		if (!licenseKey || !deviceId) {
			return next();
		}

		// Check for license abuse patterns
		const license = await prisma.license.findUnique({
			where: { licenseKey },
			include: { devices: true },
		});

		if (license) {
			// Check for rapid device additions
			const recentDeviceAdditions = license.devices.filter(
				(device: Device) =>
					Date.now() - device.firstSeen.getTime() < 24 * 60 * 60 * 1000, // 24 hours
			);

			if (recentDeviceAdditions.length > license.maxDevices) {
				await prisma.auditLog.create({
					data: {
						action: "SUSPICIOUS_ACTIVITY",
						licenseKey,
						details: {
							reason: "Rapid device additions detected",
							recentAdditions: recentDeviceAdditions.length,
							maxDevices: license.maxDevices,
						},
					},
				});

				// Temporarily suspend license validation
				return res.status(429).json({
					error: "License temporarily suspended due to suspicious activity",
					retryAfter: 3600,
				});
			}
		}

		next();
	} catch (error) {
		console.error("Enhanced license validation error:", error);
		next();
	}
};

// Request signing middleware for additional security
export const verifyRequestSignature = (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	// Skip signature verification for certain endpoints or in development
	if (process.env.NODE_ENV === "development" || req.path === "/health") {
		return next();
	}

	const signature = req.get("X-Request-Signature");
	const timestamp = req.get("X-Request-Timestamp");
	const body = JSON.stringify(req.body);

	if (!signature || !timestamp) {
		return res.status(401).json({ error: "Missing request signature" });
	}

	// Check timestamp to prevent replay attacks
	const now = Date.now();
	const requestTime = Number.parseInt(timestamp);

	if (Math.abs(now - requestTime) > 300000) {
		// 5 minutes
		return res.status(401).json({ error: "Request timestamp too old" });
	}

	// Verify signature
	const expectedSignature = crypto
		.createHmac(
			"sha256",
			process.env.REQUEST_SIGNING_SECRET || "fallback-secret",
		)
		.update(`${timestamp}${body}`)
		.digest("hex");

	if (signature !== expectedSignature) {
		return res.status(401).json({ error: "Invalid request signature" });
	}

	next();
};
