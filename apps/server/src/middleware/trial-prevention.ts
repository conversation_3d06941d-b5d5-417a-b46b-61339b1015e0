/**
 * Middleware to prevent new trial registrations while maintaining backward compatibility
 * This middleware can be applied to any endpoint that might create trial licenses
 */

import type { NextFunction, Request, Response } from "express";
import { isTrialRegistrationAllowed } from "@/services/license";
import { ErrorCode, sendErrorResponse } from "@/utils/errors";
import { EndpointPrefix, Logger } from "@/utils/logger";

/**
 * Middleware to block new trial registrations
 * Checks if the request is attempting to create a trial license and blocks it if trials are disabled
 */
export function preventTrialRegistration(
	req: Request,
	res: Response,
	next: NextFunction,
): void {
	try {
		// Check if this is a trial registration attempt
		const { licenseType } = req.body;

		if (licenseType === "trial") {
			// Check if trial registrations are allowed
			if (!isTrialRegistrationAllowed()) {
				Logger.info(
					EndpointPrefix.GENERAL,
					`Trial registration attempt blocked from IP: ${req.ip}`,
					{
						body: {
							userAgent: req.get("User-Agent"),
							referer: req.get("Referer"),
							endpoint: req.originalUrl,
						},
					},
				);

				sendErrorResponse(
					res,
					"Trial registrations no longer available",
					400,
					ErrorCode.VALIDATION_ERROR,
					"Free trials are no longer available for new users. Please purchase a license directly. Existing trial users can continue using their current licenses and can upgrade to paid plans at any time.",
					{
						trialRegistrationAllowed: false,
						alternativeOptions: [
							"Purchase a Standard license ($4.99) for 2 devices",
							"Purchase an Extended license ($9.99) for 5 devices",
							"Add additional devices for $1.99 each",
						],
						upgradeEndpoint: "/api/licenses/trial/convert",
						pricingEndpoint: "/api/payments/pricing",
					},
				);
				return;
			}
		}

		// If not a trial registration or trials are allowed, continue
		next();
	} catch (error) {
		Logger.error(
			EndpointPrefix.GENERAL,
			`Error in trial prevention middleware: ${error}`,
			{ body: { error: String(error) } },
		);

		// Don't block the request if there's an error in the middleware
		next();
	}
}

/**
 * Middleware to add trial status information to responses
 * This helps frontend applications understand trial availability
 */
export function addTrialStatusInfo(
	_req: Request,
	res: Response,
	next: NextFunction,
): void {
	try {
		// Store original json method
		const originalJson = res.json;

		// Override json method to add trial status
		res.json = function (body: Record<string, unknown>) {
			// Add trial status information to the response
			const enhancedBody = {
				...body,
				trialInfo: {
					registrationAllowed: isTrialRegistrationAllowed(),
					message: isTrialRegistrationAllowed()
						? "Trial registrations are available"
						: "Trial registrations are no longer available for new users",
				},
			};

			// Call original json method with enhanced body
			return originalJson.call(this, enhancedBody);
		};

		next();
	} catch (error) {
		Logger.error(
			EndpointPrefix.GENERAL,
			`Error in trial status middleware: ${error}`,
			{ body: { error: String(error) } },
		);

		// Don't block the request if there's an error in the middleware
		next();
	}
}

/**
 * Check if a request is from an existing trial user
 * This can be used to provide different messaging to existing vs new users
 */
export async function isExistingTrialUser(email: string): Promise<boolean> {
	try {
		const { getTrialLicenseInfo } = await import("@/services/license");
		const trialInfo = await getTrialLicenseInfo(email);
		return trialInfo.hasTrialLicense;
	} catch (error) {
		Logger.error(
			EndpointPrefix.GENERAL,
			`Error checking existing trial user: ${error}`,
			{ body: { error: String(error), email } },
		);
		return false;
	}
}

/**
 * Middleware to provide different messaging for existing trial users
 */
export function handleExistingTrialUsers(
	_req: Request,
	_res: Response,
	next: NextFunction,
): void {
	// This middleware can be enhanced to provide personalized messaging
	// for existing trial users vs completely new users
	next();
}
