/**
 * Authentication and Authorization Middleware for RBAC
 * Integrates with better-auth for session management and provides role-based access control
 */

import { fromNodeHeaders } from "better-auth/node";
import type { NextFunction, Request, Response } from "express";
import type { User } from "prisma/generated/client";
import { auth } from "@/lib/auth";
import { ErrorCode, sendErrorResponse } from "@/utils/errors";
import { EndpointPrefix, Logger } from "@/utils/logger";
import prisma from "../../prisma";

// Better-auth returns an object with both session and user properties
type BetterAuthSessionResponse = {
	session: {
		id: string;
		userId: string;
		expiresAt: Date;
		createdAt: Date;
		updatedAt: Date;
		token: string;
		ipAddress?: string | null;
		userAgent?: string | null;
	};
	user: {
		id: string;
		email: string;
		name: string;
		emailVerified: boolean;
		image?: string | null;
		createdAt: Date;
		updatedAt: Date;
	};
};

// Extend Express Request interface to include user information
declare global {
	namespace Express {
		interface Request {
			user?: User;
			session?: BetterAuthSessionResponse;
		}
	}
}

// User roles in hierarchical order (higher index = more permissions)
export const USER_ROLE_HIERARCHY = [
	"VIEWER",
	"USER",
	"MANAGER",
	"ADMIN",
	"SUPER_ADMIN",
] as const;

export type UserRole = (typeof USER_ROLE_HIERARCHY)[number];

/**
 * Permission definitions for each role
 */
export const ROLE_PERMISSIONS = {
	VIEWER: {
		canViewUsers: false,
		canManageUsers: false,
		canManageRoles: false,
		canInviteUsers: false,
		canDeactivateUsers: false,
		canViewAuditLogs: false,
		canManageSystem: false,
	},
	USER: {
		canViewUsers: false,
		canManageUsers: false,
		canManageRoles: false,
		canInviteUsers: false,
		canDeactivateUsers: false,
		canViewAuditLogs: false,
		canManageSystem: false,
	},
	MANAGER: {
		canViewUsers: true,
		canManageUsers: true, // Can manage users with lower roles
		canManageRoles: false, // Cannot change roles
		canInviteUsers: true,
		canDeactivateUsers: true, // Can deactivate users with lower roles
		canViewAuditLogs: true,
		canManageSystem: false,
	},
	ADMIN: {
		canViewUsers: true,
		canManageUsers: true,
		canManageRoles: true, // Can change roles (except SUPER_ADMIN)
		canInviteUsers: true,
		canDeactivateUsers: true,
		canViewAuditLogs: true,
		canManageSystem: false,
	},
	SUPER_ADMIN: {
		canViewUsers: true,
		canManageUsers: true,
		canManageRoles: true,
		canInviteUsers: true,
		canDeactivateUsers: true,
		canViewAuditLogs: true,
		canManageSystem: true,
	},
} as const;

/**
 * Extract user session from better-auth
 */
export async function extractUserSession(req: Request): Promise<{
	user: User | null;
	session: BetterAuthSessionResponse | null;
}> {
	try {
		// Get session from better-auth
		const session = await auth.api.getSession({
			headers: fromNodeHeaders(req.headers),
		});

		if (!session?.user) {
			return { user: null, session: null };
		}

		// Get full user data from database including role information
		const user = await prisma.user.findUnique({
			where: { id: session.user.id },
		});

		return { user, session };
	} catch (error) {
		Logger.error(
			EndpointPrefix.AUTH,
			`Failed to extract user session: ${error}`,
			{ body: { error: String(error) } },
		);
		return { user: null, session: null };
	}
}

/**
 * Authentication middleware - ensures user is logged in
 */
export async function requireAuth(
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> {
	try {
		const { user, session } = await extractUserSession(req);

		if (!user || !session) {
			return sendErrorResponse(
				res,
				"Authentication required",
				401,
				ErrorCode.UNAUTHORIZED,
				"You must be logged in to access this resource",
			);
		}

		// Check if user account is active
		if (!user.isActive) {
			return sendErrorResponse(
				res,
				"Account deactivated",
				403,
				ErrorCode.FORBIDDEN,
				"Your account has been deactivated",
			);
		}

		// Update last login time
		await prisma.user.update({
			where: { id: user.id },
			data: { lastLoginAt: new Date() },
		});

		// Attach user and session to request
		req.user = user;
		req.session = session;

		// Log successful authentication
		Logger.info(EndpointPrefix.AUTH, `User authenticated: ${user.email}`, {
			body: {
				userId: user.id,
				role: user.role,
				email: user.email,
			},
		});

		next();
	} catch (error) {
		Logger.error(
			EndpointPrefix.AUTH,
			`Authentication middleware error: ${error}`,
			{ body: { error: String(error) } },
		);

		return sendErrorResponse(
			res,
			"Authentication failed",
			500,
			ErrorCode.INTERNAL_ERROR,
			"An error occurred during authentication",
		);
	}
}

/**
 * Authorization middleware factory - checks if user has required role or higher
 */
export function requireRole(...allowedRoles: UserRole[]) {
	return async (
		req: Request,
		res: Response,
		next: NextFunction,
	): Promise<void> => {
		try {
			// Ensure user is authenticated first
			if (!req.user) {
				return sendErrorResponse(
					res,
					"Authentication required",
					401,
					ErrorCode.UNAUTHORIZED,
					"You must be logged in to access this resource",
				);
			}

			const userRole = req.user.role as UserRole;
			const userRoleIndex = USER_ROLE_HIERARCHY.indexOf(userRole);

			// Check if user has any of the allowed roles or higher
			const hasPermission = allowedRoles.some((role) => {
				const requiredRoleIndex = USER_ROLE_HIERARCHY.indexOf(role);
				return userRoleIndex >= requiredRoleIndex;
			});

			if (!hasPermission) {
				// Log unauthorized access attempt
				await prisma.auditLog.create({
					data: {
						action: "UNAUTHORIZED_ACCESS_ATTEMPT",
						userId: req.user.id,
						ipAddress: req.ip,
						userAgent: req.get("User-Agent"),
						details: {
							endpoint: req.originalUrl,
							method: req.method,
							userRole,
							requiredRoles: allowedRoles,
						},
					},
				});

				return sendErrorResponse(
					res,
					"Insufficient permissions",
					403,
					ErrorCode.FORBIDDEN,
					`This action requires one of the following roles: ${allowedRoles.join(", ")}`,
				);
			}

			next();
		} catch (error) {
			Logger.error(
				EndpointPrefix.AUTH,
				`Authorization middleware error: ${error}`,
				{ body: { error: String(error) } },
			);

			return sendErrorResponse(
				res,
				"Authorization failed",
				500,
				ErrorCode.INTERNAL_ERROR,
				"An error occurred during authorization",
			);
		}
	};
}

/**
 * Check if user can manage another user (hierarchical permissions)
 */
export function canManageUser(
	managerRole: UserRole,
	targetRole: UserRole,
): boolean {
	const managerIndex = USER_ROLE_HIERARCHY.indexOf(managerRole);
	const targetIndex = USER_ROLE_HIERARCHY.indexOf(targetRole);

	// Can only manage users with lower roles
	return managerIndex > targetIndex;
}

/**
 * Get user permissions based on role
 */
export function getUserPermissions(role: UserRole) {
	return ROLE_PERMISSIONS[role];
}

/**
 * Check if user has specific permission
 */
export function hasPermission(
	userRole: UserRole,
	permission: keyof typeof ROLE_PERMISSIONS.SUPER_ADMIN,
): boolean {
	return ROLE_PERMISSIONS[userRole][permission];
}
