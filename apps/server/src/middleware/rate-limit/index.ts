import rateLimit from "express-rate-limit";
import { ErrorCode, sendErrorResponse } from "@/utils/errors";

/**
 * Enhanced rate limiting with standardized error responses
 */

// General rate limiting configuration
const defaultWindowMs = Number.parseInt(
	process.env.RATE_LIMIT_WINDOW_MS || "900000",
); // 15 minutes
const defaultMaxRequests = Number.parseInt(
	process.env.RATE_LIMIT_MAX_REQUESTS || "100",
);

// Create license rate limiting (more restrictive)
const createLicenseLimit = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	max: 5, // limit each IP to 5 requests per windowMs
	standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
	legacyHeaders: false, // Disable the `X-RateLimit-*` headers
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many license creation requests",
			429,
			ErrorCode.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for license creation. Please try again later.",
			{
				windowMs: 15 * 60 * 1000,
				maxRequests: 5,
				retryAfter: Math.ceil((15 * 60 * 1000) / 1000), // seconds
			},
		);
	},
});

// Validate license rate limiting
const validateLicenseLimit = rateLimit({
	windowMs: 1 * 60 * 1000, // 1 minute
	max: 30, // 30 requests per minute per IP
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many validation requests",
			429,
			ErrorCode.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for license validation. Please try again later.",
			{
				windowMs: 1 * 60 * 1000,
				maxRequests: 30,
				retryAfter: 60, // seconds
			},
		);
	},
});

// Resend license rate limiting (most restrictive)
const resendLicenseLimit = rateLimit({
	windowMs: 60 * 60 * 1000, // 1 hour
	max: 3, // 3 resend requests per hour
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many resend requests",
			429,
			ErrorCode.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for license resend. Please try again later.",
			{
				windowMs: 60 * 60 * 1000,
				maxRequests: 3,
				retryAfter: 3600, // seconds (1 hour)
			},
		);
	},
});

// Payment rate limiting
const paymentLimit = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	max: 10, // 10 payment attempts per 15 minutes
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many payment requests",
			429,
			ErrorCode.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for payment processing. Please try again later.",
			{
				windowMs: 15 * 60 * 1000,
				maxRequests: 10,
				retryAfter: Math.ceil((15 * 60 * 1000) / 1000), // seconds
			},
		);
	},
});

// General API rate limiting
const generalApiLimit = rateLimit({
	windowMs: defaultWindowMs,
	max: defaultMaxRequests,
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Rate limit exceeded",
			429,
			ErrorCode.RATE_LIMIT_EXCEEDED,
			"You have exceeded the general API rate limit. Please try again later.",
			{
				windowMs: defaultWindowMs,
				maxRequests: defaultMaxRequests,
				retryAfter: Math.ceil(defaultWindowMs / 1000), // seconds
			},
		);
	},
});

// User management rate limiting
const createUserLimit = rateLimit({
	windowMs: 60 * 60 * 1000, // 1 hour
	max: 10, // 10 user creations per hour per IP
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many user creation requests",
			429,
			ErrorCode.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for user creation. Please try again later.",
			{
				windowMs: 60 * 60 * 1000,
				maxRequests: 10,
				retryAfter: 3600, // seconds (1 hour)
			},
		);
	},
});

// User invitation rate limiting (most restrictive to prevent spam)
const inviteUserLimit = rateLimit({
	windowMs: 60 * 60 * 1000, // 1 hour
	max: 20, // 20 invitations per hour per IP
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many invitation requests",
			429,
			ErrorCode.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for sending invitations. Please try again later.",
			{
				windowMs: 60 * 60 * 1000,
				maxRequests: 20,
				retryAfter: 3600, // seconds (1 hour)
			},
		);
	},
});

// User update rate limiting
const updateUserLimit = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	max: 50, // 50 user updates per 15 minutes per IP
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many user update requests",
			429,
			ErrorCode.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for user updates. Please try again later.",
			{
				windowMs: 15 * 60 * 1000,
				maxRequests: 50,
				retryAfter: Math.ceil((15 * 60 * 1000) / 1000), // seconds
			},
		);
	},
});

// User listing rate limiting (more lenient for read operations)
const listUsersLimit = rateLimit({
	windowMs: 5 * 60 * 1000, // 5 minutes
	max: 100, // 100 list requests per 5 minutes per IP
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many user listing requests",
			429,
			ErrorCode.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for user listing. Please try again later.",
			{
				windowMs: 5 * 60 * 1000,
				maxRequests: 100,
				retryAfter: Math.ceil((5 * 60 * 1000) / 1000), // seconds
			},
		);
	},
});

export {
	createLicenseLimit,
	validateLicenseLimit,
	resendLicenseLimit,
	paymentLimit,
	generalApiLimit,
	// User management rate limits
	createUserLimit,
	inviteUserLimit,
	updateUserLimit,
	listUsersLimit,
};
