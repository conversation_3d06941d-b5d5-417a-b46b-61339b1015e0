import stripe, { type Stripe } from "@/lib/stripe";
import { EndpointPrefix, Logger } from "@/utils/logger";
import prisma from "../../prisma";

/**
 * Payment service for handling Stripe payments and checkout sessions
 */

// Pricing configuration
export const PRICING: Record<string, number> = {
	standard: 499, // $4.99 in cents
	extended: 999, // $9.99 in cents
	additionalDevice: 199, // $1.99 per additional device
};

export interface CreatePaymentIntentOptions {
	licenseType: "standard" | "extended";
	additionalDevices?: number;
	email: string;
	deviceId?: string;
}

export interface CreatePaymentIntentResult {
	clientSecret: string;
	amount: number;
	licenseType: string;
	paymentIntentId: string;
}

export interface CreateCheckoutSessionOptions {
	licenseType: "standard" | "extended";
	additionalDevices?: number;
	email: string;
	deviceId?: string;
	successUrl: string;
	cancelUrl: string;
}

export interface CreateCheckoutSessionResult {
	sessionId: string;
	url: string | null;
	amount: number;
	licenseType: string;
}

/**
 * Create a payment intent for embedded Stripe Elements
 */
export async function createPaymentIntent(
	options: CreatePaymentIntentOptions,
): Promise<CreatePaymentIntentResult> {
	Logger.info(
		EndpointPrefix.PAYMENT_CREATE_INTENT,
		`Creating payment intent for: ${options.licenseType}`,
		{
			body: {
				licenseType: options.licenseType,
				additionalDevices: options.additionalDevices,
				email: options.email,
			},
		},
	);

	if (!["standard", "extended"].includes(options.licenseType)) {
		throw new Error("Invalid license type");
	}

	let amount = PRICING[options.licenseType];

	// Add cost for additional devices
	if (options.additionalDevices && options.additionalDevices > 0) {
		amount += options.additionalDevices * PRICING.additionalDevice;
	}

	const paymentIntent = await stripe.paymentIntents.create({
		amount,
		currency: "usd",
		automatic_payment_methods: {
			enabled: true,
		},
		metadata: {
			licenseType: options.licenseType,
			additionalDevices: (options.additionalDevices || 0).toString(),
			email: options.email,
			deviceId: options.deviceId || "",
			flow_type: "embedded",
		},
	});

	if (!paymentIntent.client_secret) {
		throw new Error("Payment intent client secret is missing");
	}

	return {
		clientSecret: paymentIntent.client_secret,
		amount,
		licenseType: options.licenseType,
		paymentIntentId: paymentIntent.id,
	};
}

/**
 * Create a checkout session for redirect-based payments
 */
export async function createCheckoutSession(
	options: CreateCheckoutSessionOptions,
): Promise<CreateCheckoutSessionResult> {
	Logger.info(
		EndpointPrefix.PAYMENT_CREATE_CHECKOUT,
		`Creating checkout session for: ${options.licenseType}`,
	);

	// Prevent trial payments (trials should be free, not paid)
	if (options.licenseType === "trial") {
		throw new Error("Trial licenses cannot be purchased - they should be free");
	}

	if (!["standard", "extended"].includes(options.licenseType)) {
		throw new Error("Invalid license type");
	}

	let amount = PRICING[options.licenseType];

	// Add cost for additional devices
	if (options.additionalDevices && options.additionalDevices > 0) {
		amount += options.additionalDevices * PRICING.additionalDevice;
	}

	const session = await stripe.checkout.sessions.create({
		payment_method_types: ["card"],
		line_items: [
			{
				price_data: {
					currency: "usd",
					product_data: {
						name: `${options.licenseType.charAt(0).toUpperCase() + options.licenseType.slice(1)} License`,
						description: `License for ${PRICING[options.licenseType] === 499 ? "2" : "5"} devices${options.additionalDevices && options.additionalDevices > 0 ? ` + ${options.additionalDevices} additional devices` : ""}`,
					},
					unit_amount: amount,
				},
				quantity: 1,
			},
		],
		mode: "payment",
		success_url: options.successUrl,
		cancel_url: options.cancelUrl,
		customer_email: options.email,
		// Set metadata ONLY on the checkout session - this is what we'll receive in checkout.session.completed
		metadata: {
			licenseType: options.licenseType,
			additionalDevices: (options.additionalDevices || 0).toString(),
			email: options.email,
			deviceId: options.deviceId || "",
			flow_type: "checkout",
		},
	});

	return {
		sessionId: session.id,
		url: session.url,
		amount,
		licenseType: options.licenseType,
	};
}

/**
 * Create payment intent for license upgrade (additional devices)
 */
export async function createUpgradePaymentIntent(
	licenseKey: string,
	additionalDevices: number,
): Promise<CreatePaymentIntentResult> {
	Logger.info(
		EndpointPrefix.PAYMENT_CREATE_INTENT,
		`Creating upgrade payment intent for license: ${licenseKey.substring(0, 4)}...`,
		{ body: { additionalDevices } },
	);

	// Verify license exists
	const license = await prisma.license.findUnique({
		where: { licenseKey },
	});

	if (!license) {
		throw new Error("License not found");
	}

	const amount = additionalDevices * PRICING.additionalDevice;

	const paymentIntent = await stripe.paymentIntents.create({
		amount,
		currency: "usd",
		automatic_payment_methods: {
			enabled: true,
		},
		metadata: {
			licenseKey,
			additionalDevices: additionalDevices.toString(),
			flow_type: "upgrade",
		},
	});

	if (!paymentIntent.client_secret) {
		throw new Error("Payment intent client secret is missing");
	}

	return {
		clientSecret: paymentIntent.client_secret,
		amount,
		licenseType: "upgrade",
		paymentIntentId: paymentIntent.id,
	};
}

/**
 * Get payment status from Stripe
 */
export async function getPaymentStatus(
	paymentIntentId: string,
): Promise<Stripe.PaymentIntent> {
	Logger.info(
		EndpointPrefix.PAYMENT_STATUS,
		`Getting payment status for: ${paymentIntentId}`,
	);

	return await stripe.paymentIntents.retrieve(paymentIntentId);
}

/**
 * Get checkout session details with associated license information
 */
export async function getCheckoutSessionDetails(sessionId: string) {
	try {
		// Retrieve checkout session from Stripe
		const session = await stripe.checkout.sessions.retrieve(sessionId);

		if (!session) {
			return {
				success: false,
				error: "Session not found",
			};
		}

		// Check if license was created for this session
		let license = null;
		if (session.payment_intent) {
			license = await prisma.license.findFirst({
				where: { stripePaymentIntentId: session.payment_intent as string },
				select: {
					licenseKey: true,
					licenseType: true,
					maxDevices: true,
					expiresAt: true,
					email: true,
				},
			});
		}

		return {
			success: true,
			data: {
				sessionId: session.id,
				status: session.status,
				paymentStatus: session.payment_status,
				amountTotal: session.amount_total,
				currency: session.currency,
				customerEmail: session.customer_email,
				license: license || null,
				metadata: session.metadata,
			},
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_STATUS,
			`Failed to retrieve checkout session details: ${error}`,
			{ body: { sessionId, error: String(error) } },
		);

		return {
			success: false,
			error: "Failed to retrieve checkout session",
		};
	}
}

/**
 * Get payment intent details with associated license information
 */
export async function getPaymentIntentDetails(paymentIntentId: string) {
	try {
		// Retrieve payment intent from Stripe
		const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

		if (!paymentIntent) {
			return {
				success: false,
				error: "Payment intent not found",
			};
		}

		// Check if license was created for this payment
		let license = null;
		if (paymentIntent.status === "succeeded") {
			license = await prisma.license.findFirst({
				where: { stripePaymentIntentId: paymentIntent.id },
				select: {
					licenseKey: true,
					licenseType: true,
					maxDevices: true,
					expiresAt: true,
					email: true,
				},
			});
		}

		return {
			success: true,
			data: {
				paymentIntentId: paymentIntent.id,
				status: paymentIntent.status,
				amount: paymentIntent.amount,
				currency: paymentIntent.currency,
				license: license || null,
				metadata: paymentIntent.metadata,
			},
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_STATUS,
			`Failed to retrieve payment intent details: ${error}`,
			{ body: { paymentIntentId, error: String(error) } },
		);

		return {
			success: false,
			error: "Failed to retrieve payment intent",
		};
	}
}

/**
 * Get pricing information
 */
export function getPricingInfo() {
	return {
		free: {
			price: 0,
			maxDevices: 1,
			duration: "30 days",
			limitations: "Basic features only",
		},
		standard: {
			price: PRICING.standard,
			maxDevices: 2,
			duration: "Lifetime",
		},
		extended: {
			price: PRICING.extended,
			maxDevices: 5,
			duration: "Lifetime",
		},
		additionalDevice: {
			price: PRICING.additionalDevice,
			description: "Per additional device",
		},
	};
}
