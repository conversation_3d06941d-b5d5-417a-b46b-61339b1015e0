import type { License } from "prisma/generated/client";
import stripe, { type Stripe } from "@/lib/stripe";
import { sendRefundConfirmationEmail } from "@/templates";
import { EndpointPrefix, Logger } from "@/utils/logger";
import prisma from "../../prisma";

/**
 * Refund service for handling Stripe refunds and database updates
 */

export interface RefundOptions {
	licenseId: string;
	reason: string;
	requestedBy: string;
	amount?: number; // Optional for partial refunds
	adminNotes?: string;
}

export interface RefundResult {
	success: boolean;
	refundId?: string;
	amount?: number;
	error?: string;
	stripeRefund?: Stripe.Refund;
}

/**
 * Process a full refund for a license
 */
export async function processFullRefund(
	license: License,
	options: Omit<RefundOptions, "licenseId" | "amount">,
): Promise<RefundResult> {
	try {
		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Processing full refund for license: ${license.licenseKey}`,
			{ body: { licenseId: license.id, reason: options.reason } },
		);

		if (!license.stripePaymentIntentId) {
			return {
				success: false,
				error: "License has no associated payment to refund",
			};
		}

		if (license.refundedAt) {
			return {
				success: false,
				error: "License has already been refunded",
			};
		}

		// Retrieve the original payment intent to get the amount
		const paymentIntent = await stripe.paymentIntents.retrieve(
			license.stripePaymentIntentId,
		);

		if (paymentIntent.status !== "succeeded") {
			return {
				success: false,
				error: "Original payment was not successful",
			};
		}

		// Create the refund in Stripe
		const stripeRefund = await stripe.refunds.create({
			payment_intent: license.stripePaymentIntentId,
			reason: "requested_by_customer",
			metadata: {
				licenseKey: license.licenseKey,
				licenseId: license.id,
				refundReason: options.reason,
				requestedBy: options.requestedBy,
			},
		});

		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Stripe refund created: ${stripeRefund.id}`,
			{
				body: {
					refundId: stripeRefund.id,
					amount: stripeRefund.amount,
					status: stripeRefund.status,
				},
			},
		);

		return {
			success: true,
			refundId: stripeRefund.id,
			amount: stripeRefund.amount,
			stripeRefund,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Failed to process Stripe refund: ${error instanceof Error ? error.message : String(error)}`,
			{ body: { licenseId: license.id, error: String(error) } },
		);

		return {
			success: false,
			error: error instanceof Error ? error.message : "Unknown error occurred",
		};
	}
}

/**
 * Process a partial refund for a license
 */
export async function processPartialRefund(
	license: License,
	options: RefundOptions,
): Promise<RefundResult> {
	try {
		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Processing partial refund for license: ${license.licenseKey}`,
			{
				body: {
					licenseId: license.id,
					amount: options.amount,
					reason: options.reason,
				},
			},
		);

		if (!license.stripePaymentIntentId) {
			return {
				success: false,
				error: "License has no associated payment to refund",
			};
		}

		if (license.refundedAt) {
			return {
				success: false,
				error: "License has already been refunded",
			};
		}

		if (!options.amount || options.amount <= 0) {
			return {
				success: false,
				error: "Invalid refund amount specified",
			};
		}

		// Retrieve the original payment intent to validate the amount
		const paymentIntent = await stripe.paymentIntents.retrieve(
			license.stripePaymentIntentId,
		);

		if (paymentIntent.status !== "succeeded") {
			return {
				success: false,
				error: "Original payment was not successful",
			};
		}

		if (options.amount > paymentIntent.amount) {
			return {
				success: false,
				error: "Refund amount cannot exceed original payment amount",
			};
		}

		// Create the partial refund in Stripe
		const stripeRefund = await stripe.refunds.create({
			payment_intent: license.stripePaymentIntentId,
			amount: options.amount,
			reason: "requested_by_customer",
			metadata: {
				licenseKey: license.licenseKey,
				licenseId: license.id,
				refundReason: options.reason,
				requestedBy: options.requestedBy,
				refundType: "partial",
			},
		});

		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Stripe partial refund created: ${stripeRefund.id}`,
			{
				body: {
					refundId: stripeRefund.id,
					amount: stripeRefund.amount,
					originalAmount: paymentIntent.amount,
					status: stripeRefund.status,
				},
			},
		);

		return {
			success: true,
			refundId: stripeRefund.id,
			amount: stripeRefund.amount,
			stripeRefund,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Failed to process partial Stripe refund: ${error instanceof Error ? error.message : String(error)}`,
			{ body: { licenseId: license.id, error: String(error) } },
		);

		return {
			success: false,
			error: error instanceof Error ? error.message : "Unknown error occurred",
		};
	}
}

/**
 * Update license and devices after successful refund
 */
export async function updateLicenseAfterRefund(
	license: License,
	refundResult: RefundResult,
	options: RefundOptions,
): Promise<void> {
	if (!refundResult.success || !refundResult.refundId) {
		throw new Error("Cannot update license for failed refund");
	}

	try {
		await prisma.$transaction(async (tx) => {
			// Update the license with refund information
			await tx.license.update({
				where: { id: license.id },
				data: {
					refundedAt: new Date(),
					refundReason: options.reason,
					refundAmount: refundResult.amount,
					stripeRefundId: refundResult.refundId,
				},
			});

			// Deactivate all associated devices
			await tx.device.updateMany({
				where: { licenseId: license.id },
				data: { isActive: false },
			});

			// Update refund request status if it exists
			const refundRequest = await tx.refundRequest.findUnique({
				where: { licenseId: license.id },
			});

			if (refundRequest) {
				await tx.refundRequest.update({
					where: { licenseId: license.id },
					data: {
						status: "PROCESSED",
						processedAt: new Date(),
						processedBy: options.requestedBy,
						adminNotes: options.adminNotes,
					},
				});
			}

			// Create audit log entry
			await tx.auditLog.create({
				data: {
					action: "REFUND_PROCESSED",
					licenseKey: license.licenseKey,
					details: {
						refundId: refundResult.refundId,
						amount: refundResult.amount,
						reason: options.reason,
						requestedBy: options.requestedBy,
						adminNotes: options.adminNotes,
						devicesDeactivated: true,
					},
				},
			});
		});

		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`License updated after successful refund: ${license.licenseKey}`,
			{
				body: {
					licenseId: license.id,
					refundId: refundResult.refundId,
					amount: refundResult.amount,
				},
			},
		);

		// Send refund confirmation email
		try {
			await sendRefundConfirmationEmail(
				license.email,
				license.licenseKey,
				refundResult.amount || 0,
				options.reason,
				refundResult.refundId || "",
			);

			Logger.info(
				EndpointPrefix.PAYMENT_WEBHOOK,
				`Refund confirmation email sent: ${license.email}`,
				{ body: { licenseId: license.id, refundId: refundResult.refundId } },
			);
		} catch (emailError) {
			Logger.error(
				EndpointPrefix.PAYMENT_WEBHOOK,
				`Failed to send refund confirmation email: ${emailError instanceof Error ? emailError.message : String(emailError)}`,
				{ body: { licenseId: license.id, email: license.email } },
			);
			// Don't throw here - email failure shouldn't fail the refund process
		}
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Failed to update license after refund: ${error instanceof Error ? error.message : String(error)}`,
			{ body: { licenseId: license.id, error: String(error) } },
		);
		throw error;
	}
}

/**
 * Check if a license is eligible for refund
 */
export function isRefundEligible(license: License): {
	eligible: boolean;
	reason?: string;
} {
	if (!license.stripePaymentIntentId) {
		return {
			eligible: false,
			reason: "License has no associated payment",
		};
	}

	if (license.refundedAt) {
		return {
			eligible: false,
			reason: "License has already been refunded",
		};
	}

	// Add any additional business rules here

	const daysSinceCreation = Math.floor(
		(Date.now() - license.createdAt.getTime()) / (1000 * 60 * 60 * 24),
	);
	if (daysSinceCreation > 30) {
		return {
			eligible: false,
			reason: "Refund period has expired (30 days)",
		};
	}

	return { eligible: true };
}
