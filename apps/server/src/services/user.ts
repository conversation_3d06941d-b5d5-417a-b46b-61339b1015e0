/**
 * User management service layer
 * Handles user creation, updates, invitations, and role management with proper permission checks
 */

import type {
	CreateUserRequest,
	ListUsersRequest,
	SendInvitationRequest,
	UpdateUserRequest,
} from "@snapback/shared";
import crypto from "crypto";
import type { User, UserInvitation } from "prisma/generated/client";
import type { UserRole } from "@/middleware/auth";
import { EndpointPrefix, Logger } from "@/utils/logger";
import {
	canUserAssignRole,
	canUserInviteWithRole,
	canUserManageUser,
	filterViewableUsers,
	isRoleTransitionAllowed,
	userHasPermission,
} from "@/utils/permissions";
import prisma from "../../prisma";

export interface CreateUserResult {
	success: boolean;
	user?: User;
	error?: string;
	errorCode?: string;
}

export interface UpdateUserResult {
	success: boolean;
	user?: User;
	error?: string;
	errorCode?: string;
}

export interface SendInvitationResult {
	success: boolean;
	invitation?: UserInvitation;
	error?: string;
	errorCode?: string;
}

export interface ListUsersResult {
	success: boolean;
	users?: User[];
	pagination?: {
		page: number;
		limit: number;
		totalCount: number;
		totalPages: number;
		hasNextPage: boolean;
		hasPreviousPage: boolean;
	};
	error?: string;
	errorCode?: string;
}

/**
 * Generate a secure invitation token
 */
function generateInvitationToken(): string {
	return crypto.randomBytes(16).toString("hex");
}

/**
 * Create a new user (internal creation, not via invitation)
 */
export async function createUser(
	currentUser: User,
	userData: CreateUserRequest,
): Promise<CreateUserResult> {
	try {
		Logger.info(
			EndpointPrefix.USER_CREATE,
			`Creating user: ${userData.email}`,
			{
				body: {
					email: userData.email,
					role: userData.role,
					createdBy: currentUser.id,
				},
			},
		);

		// Check if current user can create users
		if (!userHasPermission(currentUser, "canManageUsers")) {
			return {
				success: false,
				error: "Insufficient permissions to create users",
				errorCode: "INSUFFICIENT_PERMISSIONS",
			};
		}

		// Check if current user can assign the requested role
		if (!canUserAssignRole(currentUser, userData.role as UserRole)) {
			return {
				success: false,
				error: `Cannot assign role: ${userData.role}`,
				errorCode: "INVALID_ROLE_ASSIGNMENT",
			};
		}

		// Check if user already exists
		const existingUser = await prisma.user.findUnique({
			where: { email: userData.email.toLowerCase() },
		});

		if (existingUser) {
			return {
				success: false,
				error: "User with this email already exists",
				errorCode: "USER_ALREADY_EXISTS",
			};
		}

		// Create the user
		const user = await prisma.user.create({
			data: {
				name: userData.name,
				email: userData.email.toLowerCase(),
				role: userData.role,
				invitedBy: currentUser.id,
				invitedAt: new Date(),
				emailVerified: false,
				isActive: true,
			},
		});

		// Log user creation
		await prisma.auditLog.create({
			data: {
				action: "USER_CREATED",
				userId: currentUser.id,
				targetUserId: user.id,
				details: {
					userEmail: user.email,
					userRole: user.role,
					createdBy: currentUser.email,
				},
			},
		});

		Logger.info(
			EndpointPrefix.USER_CREATE,
			`User created successfully: ${user.email}`,
			{
				body: {
					userId: user.id,
					email: user.email,
					role: user.role,
				},
			},
		);

		return { success: true, user };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_CREATE,
			`Failed to create user: ${error}`,
			{ body: { error: String(error) } },
		);

		return {
			success: false,
			error: "Failed to create user",
			errorCode: "INTERNAL_ERROR",
		};
	}
}

/**
 * Update user information
 */
export async function updateUser(
	currentUser: User,
	targetUserId: string,
	updateData: UpdateUserRequest,
): Promise<UpdateUserResult> {
	try {
		Logger.info(EndpointPrefix.USER_UPDATE, `Updating user: ${targetUserId}`, {
			body: {
				targetUserId,
				updateData,
				updatedBy: currentUser.id,
			},
		});

		// Get target user
		const targetUser = await prisma.user.findUnique({
			where: { id: targetUserId },
		});

		if (!targetUser) {
			return {
				success: false,
				error: "User not found",
				errorCode: "USER_NOT_FOUND",
			};
		}

		// Check if current user can manage the target user
		if (!canUserManageUser(currentUser, targetUser)) {
			return {
				success: false,
				error: "Insufficient permissions to update this user",
				errorCode: "INSUFFICIENT_PERMISSIONS",
			};
		}

		// If role is being changed, validate the role transition
		if (updateData.role) {
			const roleTransition = isRoleTransitionAllowed(
				currentUser,
				targetUser,
				updateData.role as UserRole,
			);

			if (!roleTransition.allowed) {
				return {
					success: false,
					error: roleTransition.reason || "Role change not allowed",
					errorCode: "INVALID_ROLE_CHANGE",
				};
			}
		}

		// Update the user
		const updatedUser = await prisma.user.update({
			where: { id: targetUserId },
			data: {
				...(updateData.name && { name: updateData.name }),
				...(updateData.role && { role: updateData.role }),
				...(updateData.isActive !== undefined && {
					isActive: updateData.isActive,
				}),
				updatedAt: new Date(),
			},
		});

		// Log the update
		await prisma.auditLog.create({
			data: {
				action: updateData.role ? "USER_ROLE_CHANGED" : "USER_UPDATED",
				userId: currentUser.id,
				targetUserId: updatedUser.id,
				details: {
					changes: updateData,
					previousRole: targetUser.role,
					newRole: updatedUser.role,
					updatedBy: currentUser.email,
				},
			},
		});

		Logger.info(
			EndpointPrefix.USER_UPDATE,
			`User updated successfully: ${updatedUser.email}`,
			{
				body: {
					userId: updatedUser.id,
					changes: updateData,
				},
			},
		);

		return { success: true, user: updatedUser };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_UPDATE,
			`Failed to update user: ${error}`,
			{ body: { error: String(error) } },
		);

		return {
			success: false,
			error: "Failed to update user",
			errorCode: "INTERNAL_ERROR",
		};
	}
}

/**
 * Send user invitation
 */
export async function sendInvitation(
	currentUser: User,
	invitationData: SendInvitationRequest,
): Promise<SendInvitationResult> {
	try {
		Logger.info(
			EndpointPrefix.USER_INVITE,
			`Sending invitation to: ${invitationData.email}`,
			{
				body: {
					email: invitationData.email,
					role: invitationData.role,
					invitedBy: currentUser.id,
				},
			},
		);

		// Check if current user can invite users with the specified role
		if (!canUserInviteWithRole(currentUser, invitationData.role as UserRole)) {
			return {
				success: false,
				error: `Cannot invite users with role: ${invitationData.role}`,
				errorCode: "INSUFFICIENT_PERMISSIONS",
			};
		}

		// Check if user already exists
		const existingUser = await prisma.user.findUnique({
			where: { email: invitationData.email.toLowerCase() },
		});

		if (existingUser) {
			return {
				success: false,
				error: "User with this email already exists",
				errorCode: "USER_ALREADY_EXISTS",
			};
		}

		// Check if there's already a pending invitation
		const existingInvitation = await prisma.userInvitation.findUnique({
			where: { email: invitationData.email.toLowerCase() },
		});

		if (existingInvitation && existingInvitation.status === "PENDING") {
			return {
				success: false,
				error: "Invitation already sent to this email",
				errorCode: "INVITATION_ALREADY_EXISTS",
			};
		}

		// Generate invitation token and expiration (7 days)
		const token = generateInvitationToken();
		const expiresAt = new Date();
		expiresAt.setDate(expiresAt.getDate() + 7);

		// Create or update invitation
		const invitation = await prisma.userInvitation.upsert({
			where: { email: invitationData.email.toLowerCase() },
			update: {
				role: invitationData.role,
				token,
				status: "PENDING",
				invitedById: currentUser.id,
				expiresAt,
				updatedAt: new Date(),
			},
			create: {
				email: invitationData.email.toLowerCase(),
				role: invitationData.role,
				token,
				status: "PENDING",
				invitedById: currentUser.id,
				expiresAt,
			},
			include: {
				invitedBy: true,
			},
		});

		// Log invitation sent
		await prisma.auditLog.create({
			data: {
				action: "INVITATION_SENT",
				userId: currentUser.id,
				details: {
					invitationEmail: invitation.email,
					invitationRole: invitation.role,
					invitedBy: currentUser.email,
					expiresAt: invitation.expiresAt,
				},
			},
		});

		// TODO: Send invitation email
		// await sendInvitationEmail(invitation);

		Logger.info(
			EndpointPrefix.USER_INVITE,
			`Invitation sent successfully to: ${invitation.email}`,
			{
				body: {
					invitationId: invitation.id,
					email: invitation.email,
					role: invitation.role,
				},
			},
		);

		return { success: true, invitation };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_INVITE,
			`Failed to send invitation: ${error}`,
			{ body: { error: String(error) } },
		);

		return {
			success: false,
			error: "Failed to send invitation",
			errorCode: "INTERNAL_ERROR",
		};
	}
}

/**
 * List users with pagination and filtering
 */
export async function listUsers(
	currentUser: User,
	filters: ListUsersRequest,
): Promise<ListUsersResult> {
	try {
		Logger.info(EndpointPrefix.USER_LIST, "Listing users with filters", {
			body: {
				filters,
				requestedBy: currentUser.id,
			},
		});

		// Check if current user can view users
		if (!userHasPermission(currentUser, "canViewUsers")) {
			return {
				success: false,
				error: "Insufficient permissions to view users",
				errorCode: "INSUFFICIENT_PERMISSIONS",
			};
		}

		// Build where clause
		const where: {
			role?: string;
			isActive?: boolean;
			OR?: Array<{
				name?: { contains: string; mode: "insensitive" };
				email?: { contains: string; mode: "insensitive" };
			}>;
		} = {};

		if (filters.role) {
			where.role = filters.role;
		}

		if (filters.isActive !== undefined) {
			where.isActive = filters.isActive;
		}

		if (filters.search) {
			where.OR = [
				{ name: { contains: filters.search, mode: "insensitive" } },
				{ email: { contains: filters.search, mode: "insensitive" } },
			];
		}

		// Get total count
		const totalCount = await prisma.user.count({ where });

		// Calculate pagination
		const totalPages = Math.ceil(totalCount / filters.limit);
		const skip = (filters.page - 1) * filters.limit;

		// Get users
		const allUsers = await prisma.user.findMany({
			where,
			skip,
			take: filters.limit,
			orderBy: [
				{ role: "desc" }, // Higher roles first
				{ createdAt: "desc" },
			],
		});

		// Filter users based on what current user can see
		const users = filterViewableUsers(currentUser, allUsers);

		const pagination = {
			page: filters.page,
			limit: filters.limit,
			totalCount,
			totalPages,
			hasNextPage: filters.page < totalPages,
			hasPreviousPage: filters.page > 1,
		};

		Logger.info(EndpointPrefix.USER_LIST, `Listed ${users.length} users`, {
			body: {
				totalCount,
				filteredCount: users.length,
				page: filters.page,
			},
		});

		return { success: true, users, pagination };
	} catch (error) {
		Logger.error(EndpointPrefix.USER_LIST, `Failed to list users: ${error}`, {
			body: { error: String(error) },
		});

		return {
			success: false,
			error: "Failed to list users",
			errorCode: "INTERNAL_ERROR",
		};
	}
}

/**
 * Deactivate/reactivate user
 */
export async function toggleUserStatus(
	currentUser: User,
	targetUserId: string,
	isActive: boolean,
): Promise<UpdateUserResult> {
	try {
		Logger.info(
			EndpointPrefix.USER_UPDATE,
			`${isActive ? "Activating" : "Deactivating"} user: ${targetUserId}`,
			{
				body: {
					targetUserId,
					isActive,
					updatedBy: currentUser.id,
				},
			},
		);

		// Get target user
		const targetUser = await prisma.user.findUnique({
			where: { id: targetUserId },
		});

		if (!targetUser) {
			return {
				success: false,
				error: "User not found",
				errorCode: "USER_NOT_FOUND",
			};
		}

		// Check if current user can deactivate users
		if (!userHasPermission(currentUser, "canDeactivateUsers")) {
			return {
				success: false,
				error: "Insufficient permissions to change user status",
				errorCode: "INSUFFICIENT_PERMISSIONS",
			};
		}

		// Check if current user can manage the target user
		if (!canUserManageUser(currentUser, targetUser)) {
			return {
				success: false,
				error: "Insufficient permissions to manage this user",
				errorCode: "INSUFFICIENT_PERMISSIONS",
			};
		}

		// Can't deactivate yourself
		if (currentUser.id === targetUserId) {
			return {
				success: false,
				error: "Cannot change your own status",
				errorCode: "CANNOT_MODIFY_SELF",
			};
		}

		// Update user status
		const updatedUser = await prisma.user.update({
			where: { id: targetUserId },
			data: {
				isActive,
				updatedAt: new Date(),
			},
		});

		// Log the status change
		await prisma.auditLog.create({
			data: {
				action: isActive ? "USER_ACTIVATED" : "USER_DEACTIVATED",
				userId: currentUser.id,
				targetUserId: updatedUser.id,
				details: {
					previousStatus: targetUser.isActive,
					newStatus: isActive,
					changedBy: currentUser.email,
				},
			},
		});

		Logger.info(
			EndpointPrefix.USER_UPDATE,
			`User status changed successfully: ${updatedUser.email}`,
			{
				body: {
					userId: updatedUser.id,
					isActive,
				},
			},
		);

		return { success: true, user: updatedUser };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_UPDATE,
			`Failed to change user status: ${error}`,
			{ body: { error: String(error) } },
		);

		return {
			success: false,
			error: "Failed to change user status",
			errorCode: "INTERNAL_ERROR",
		};
	}
}

/**
 * Revoke user invitation
 */
export async function revokeInvitation(
	currentUser: User,
	invitationId: string,
): Promise<{ success: boolean; error?: string; errorCode?: string }> {
	try {
		Logger.info(
			EndpointPrefix.USER_REVOKE_INVITE,
			`Revoking invitation: ${invitationId}`,
			{
				body: {
					invitationId,
					revokedBy: currentUser.id,
				},
			},
		);

		// Check if current user can manage invitations
		if (!userHasPermission(currentUser, "canInviteUsers")) {
			return {
				success: false,
				error: "Insufficient permissions to revoke invitations",
				errorCode: "INSUFFICIENT_PERMISSIONS",
			};
		}

		// Get invitation
		const invitation = await prisma.userInvitation.findUnique({
			where: { id: invitationId },
		});

		if (!invitation) {
			return {
				success: false,
				error: "Invitation not found",
				errorCode: "INVITATION_NOT_FOUND",
			};
		}

		// Check if current user can revoke this invitation
		if (
			invitation.invitedById !== currentUser.id &&
			currentUser.role !== "SUPER_ADMIN"
		) {
			return {
				success: false,
				error: "Can only revoke invitations you sent",
				errorCode: "INSUFFICIENT_PERMISSIONS",
			};
		}

		// Update invitation status
		await prisma.userInvitation.update({
			where: { id: invitationId },
			data: {
				status: "REVOKED",
				updatedAt: new Date(),
			},
		});

		// Log invitation revocation
		await prisma.auditLog.create({
			data: {
				action: "INVITATION_REVOKED",
				userId: currentUser.id,
				details: {
					invitationEmail: invitation.email,
					invitationRole: invitation.role,
					revokedBy: currentUser.email,
				},
			},
		});

		Logger.info(
			EndpointPrefix.USER_REVOKE_INVITE,
			`Invitation revoked successfully: ${invitation.email}`,
			{
				body: {
					invitationId,
					email: invitation.email,
				},
			},
		);

		return { success: true };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_REVOKE_INVITE,
			`Failed to revoke invitation: ${error}`,
			{ body: { error: String(error) } },
		);

		return {
			success: false,
			error: "Failed to revoke invitation",
			errorCode: "INTERNAL_ERROR",
		};
	}
}
