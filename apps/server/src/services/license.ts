import type { Device, License } from "prisma/generated/client";
import { sendLicenseEmail } from "@/templates";
import { generateDeviceToken, generateLicenseKey, hashDeviceId } from "@/utils";
import { EndpointPrefix, Logger } from "@/utils/logger";
import prisma from "../../prisma";

/**
 * License service for handling license creation, validation, and device management
 */

export interface CreateLicenseOptions {
	email: string;
	licenseType: "trial" | "standard" | "extended";
	additionalDevices?: number;
	deviceId?: string;
	stripePaymentIntentId?: string;
	trialDurationDays?: number; // For trial licenses only
}

export interface CreateLicenseResult {
	success: boolean;
	license?: License;
	error?: string;
}

export interface ValidateLicenseOptions {
	licenseKey: string;
	deviceId: string;
	appVersion?: string;
	deviceMetadata?: {
		deviceName?: string;
		deviceType?: string;
		deviceModel?: string;
		operatingSystem?: string;
		architecture?: string;
		screenResolution?: string;
		totalMemory?: string;
		userNickname?: string;
		location?: string;
		notes?: string;
	};
}

export interface ValidateLicenseResult {
	success: boolean;
	license?: License & { devices: Device[] };
	deviceToken?: string;
	error?: string;
	errorCode?: string;
}

export interface ResendLicenseResult {
	success: boolean;
	licensesSent: number;
	error?: string;
}

/**
 * Create a new license
 */
export async function createLicense(
	options: CreateLicenseOptions,
): Promise<CreateLicenseResult> {
	try {
		Logger.info(
			EndpointPrefix.LICENSE_CREATE,
			`Creating license for: ${options.email}`,
			{
				body: {
					licenseType: options.licenseType,
					additionalDevices: options.additionalDevices,
				},
			},
		);

		const licenseKey = generateLicenseKey();

		// Determine max devices based on license type
		let maxDevices: number;
		switch (options.licenseType) {
			case "trial":
				maxDevices = 1; // Trial licenses get 1 device
				break;
			case "standard":
				maxDevices = 2;
				break;
			case "extended":
				maxDevices = 5;
				break;
			default:
				throw new Error(`Invalid license type: ${options.licenseType}`);
		}

		const totalMaxDevices = maxDevices + (options.additionalDevices || 0);

		// Calculate expiration date for trial licenses
		let expiresAt: Date | null = null;
		if (options.licenseType === "trial") {
			const trialDays = options.trialDurationDays || 14; // Default 14-day trial
			expiresAt = new Date();
			expiresAt.setDate(expiresAt.getDate() + trialDays);
		}

		const license = await prisma.license.create({
			data: {
				licenseKey,
				email: options.email.toLowerCase(),
				licenseType: options.licenseType,
				maxDevices: totalMaxDevices,
				expiresAt,
				stripePaymentIntentId: options.stripePaymentIntentId,
				createdAt: new Date(),
			},
		});

		// Send license email
		try {
			await sendLicenseEmail(
				options.email,
				licenseKey,
				options.licenseType,
				expiresAt, // Pass expiration date (null for paid licenses, date for trials)
			);
			Logger.info(
				EndpointPrefix.LICENSE_CREATE,
				`✅ License email sent successfully to: ${options.email}`,
				{ body: { licenseKey: `${licenseKey.substring(0, 8)}...` } },
			);
		} catch (emailError) {
			Logger.error(
				EndpointPrefix.LICENSE_CREATE,
				`❌ Failed to send license email to: ${options.email}`,
				{
					body: {
						error: String(emailError),
						licenseKey: `${licenseKey.substring(0, 8)}... `,
					},
				},
			);
			// Don't fail the entire license creation if email fails
			// The license is still created and can be resent later
		}

		Logger.info(
			EndpointPrefix.LICENSE_CREATE,
			`License created successfully: ${licenseKey}`,
			{ body: { licenseId: license.id } },
		);

		return {
			success: true,
			license,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_CREATE,
			`Failed to create license: ${error}`,
			{ body: { error: String(error) } },
		);

		return {
			success: false,
			error: "Failed to create license",
		};
	}
}

/**
 * Validate a license and register/update device
 */
export async function validateLicense(
	options: ValidateLicenseOptions,
): Promise<ValidateLicenseResult> {
	try {
		Logger.info(
			EndpointPrefix.LICENSE_VALIDATE,
			`Validating license: ${options.licenseKey.substring(0, 4)}...`,
			{
				body: { deviceId: options.deviceId ? "[PROVIDED]" : "[NOT_PROVIDED]" },
			},
		);

		// Find license
		const license = await prisma.license.findUnique({
			where: { licenseKey: options.licenseKey },
			include: { devices: true },
		});

		if (!license) {
			return {
				success: false,
				error: "License not found",
				errorCode: "LICENSE_NOT_FOUND",
			};
		}

		// Check if license is expired
		if (license.expiresAt && license.expiresAt < new Date()) {
			return {
				success: false,
				error: "License has expired",
				errorCode: "LICENSE_EXPIRED",
			};
		}

		// Check if license is refunded
		if (license.refundedAt) {
			return {
				success: false,
				error: "License has been refunded",
				errorCode: "LICENSE_REFUNDED",
			};
		}

		// Register or update device
		const deviceResult = await registerDevice(license, options);
		if (!deviceResult.success) {
			return {
				success: false,
				error: deviceResult.error,
				errorCode: deviceResult.errorCode,
			};
		}

		return {
			success: true,
			license,
			deviceToken: deviceResult.deviceToken,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_VALIDATE,
			`Failed to validate license: ${error}`,
			{ body: { error: String(error) } },
		);

		return {
			success: false,
			error: "Failed to validate license",
			errorCode: "INTERNAL_ERROR",
		};
	}
}

/**
 * Register or update a device for a license
 */
export async function registerDevice(
	license: License & { devices: Device[] },
	options: ValidateLicenseOptions,
): Promise<{
	success: boolean;
	deviceToken?: string;
	error?: string;
	errorCode?: string;
}> {
	try {
		// Check if device is already registered
		let existingDevice = null;
		for (const device of license.devices) {
			const { hash: testHash } = hashDeviceId(options.deviceId, device.salt);
			if (testHash === device.deviceHash) {
				existingDevice = device;
				break;
			}
		}

		if (existingDevice) {
			// Update existing device
			const updateData: Record<string, unknown> = {
				lastSeen: new Date(),
				appVersion: options.appVersion || existingDevice.appVersion,
			};

			// Add device metadata if provided
			if (options.deviceMetadata) {
				Object.entries(options.deviceMetadata).forEach(([key, value]) => {
					if (value !== undefined) {
						updateData[key] = value;
					}
				});
			}

			await prisma.device.update({
				where: { id: existingDevice.id },
				data: updateData,
			});

			const deviceToken = generateDeviceToken(
				license.id,
				existingDevice.deviceHash,
			);

			return {
				success: true,
				deviceToken,
			};
		}

		// Check if we can add a new device
		if (license.devices.length >= license.maxDevices) {
			return {
				success: false,
				error: "Maximum devices reached",
				errorCode: "DEVICE_LIMIT_EXCEEDED",
			};
		}

		// Add new device
		const { hash: newDeviceHash, salt: newSalt } = hashDeviceId(
			options.deviceId,
		);

		const newDeviceData = {
			licenseId: license.id,
			deviceHash: newDeviceHash,
			salt: newSalt,
			firstSeen: new Date(),
			lastSeen: new Date(),
			appVersion: options.appVersion,
		} as Record<string, unknown>;

		// Add device metadata if provided
		if (options.deviceMetadata) {
			Object.entries(options.deviceMetadata).forEach(([key, value]) => {
				if (value !== undefined) {
					newDeviceData[key] = value;
				}
			});
		}

		await prisma.device.create({
			data: newDeviceData as Device,
		});

		const deviceToken = generateDeviceToken(license.id, newDeviceHash);

		return {
			success: true,
			deviceToken,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_VALIDATE,
			`Failed to register device: ${error}`,
			{ body: { error: String(error) } },
		);

		return {
			success: false,
			error: "Failed to register device",
			errorCode: "INTERNAL_ERROR",
		};
	}
}

/**
 * Resend license email
 */
export async function resendLicenseEmail(
	email: string,
): Promise<ResendLicenseResult> {
	try {
		Logger.info(
			EndpointPrefix.LICENSE_RESEND,
			`Resending license for: ${email}`,
		);

		const licenses = await prisma.license.findMany({
			where: {
				email: email.toLowerCase(),
				OR: [{ expiresAt: null }, { expiresAt: { gt: new Date() } }],
			},
		});

		if (licenses.length === 0) {
			return {
				success: false,
				licensesSent: 0,
				error: "No active licenses found for email",
			};
		}

		// Send emails for all active licenses
		for (const license of licenses) {
			await sendLicenseEmail(
				email,
				license.licenseKey,
				license.licenseType,
				license.expiresAt,
			);
		}

		return {
			success: true,
			licensesSent: licenses.length,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_RESEND,
			`Failed to resend license: ${error}`,
			{ body: { error: String(error) } },
		);

		return {
			success: false,
			licensesSent: 0,
			error: "Failed to resend license",
		};
	}
}

/**
 * Update license with additional devices (for upgrades)
 */
export async function updateLicense(
	licenseKey: string,
	additionalDevices: number,
) {
	try {
		const updatedLicense = await prisma.license.update({
			where: { licenseKey },
			data: {
				maxDevices: {
					increment: additionalDevices,
				},
			},
			select: {
				licenseKey: true,
				licenseType: true,
				maxDevices: true,
				email: true,
			},
		});

		Logger.info(
			EndpointPrefix.LICENSE_UPGRADE,
			`License upgraded successfully: ${licenseKey}`,
			{ body: { additionalDevices, newMaxDevices: updatedLicense.maxDevices } },
		);

		return {
			success: true,
			data: updatedLicense,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_UPGRADE,
			`Failed to update license: ${error}`,
			{
				body: {
					licenseKey: "[REDACTED]",
					additionalDevices,
					error: String(error),
				},
			},
		);

		return {
			success: false,
			error: "Failed to update license",
		};
	}
}

/**
 * Free Trial Management Functions
 * These functions support existing free trial users while preventing new trial registrations
 */

export interface TrialStatusResult {
	success: boolean;
	isTrialLicense: boolean;
	isExpired?: boolean;
	daysRemaining?: number;
	expiresAt?: Date;
	error?: string;
}

export interface ConvertTrialResult {
	success: boolean;
	updatedLicense?: License;
	error?: string;
	errorCode?: string;
}

/**
 * Check if a license is a trial and get trial status
 */
export async function getTrialStatus(
	licenseKey: string,
): Promise<TrialStatusResult> {
	try {
		Logger.info(
			EndpointPrefix.LICENSE_STATUS,
			`Checking trial status for license: ${licenseKey.substring(0, 4)}...`,
		);

		const license = await prisma.license.findUnique({
			where: { licenseKey },
		});

		if (!license) {
			return {
				success: false,
				isTrialLicense: false,
				error: "License not found",
			};
		}

		const isTrialLicense = license.licenseType === "trial";

		if (!isTrialLicense) {
			return {
				success: true,
				isTrialLicense: false,
			};
		}

		// Calculate trial status
		const now = new Date();
		const expiresAt = license.expiresAt;
		const isExpired = expiresAt ? expiresAt < now : false;

		let daysRemaining = 0;
		if (expiresAt && !isExpired) {
			const timeDiff = expiresAt.getTime() - now.getTime();
			daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));
		}

		return {
			success: true,
			isTrialLicense: true,
			isExpired,
			daysRemaining,
			expiresAt: expiresAt || undefined,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_STATUS,
			`Failed to check trial status: ${error}`,
			{ body: { error: String(error) } },
		);

		return {
			success: false,
			isTrialLicense: false,
			error: "Failed to check trial status",
		};
	}
}

/**
 * Convert a trial license to a paid license
 * This maintains the same license key but updates the type and removes expiration
 */
export async function convertTrialToPaid(
	licenseKey: string,
	newLicenseType: "standard" | "extended",
	stripePaymentIntentId: string,
	additionalDevices = 0,
): Promise<ConvertTrialResult> {
	try {
		Logger.info(
			EndpointPrefix.LICENSE_UPGRADE,
			`Converting trial to ${newLicenseType} license: ${licenseKey.substring(0, 4)}...`,
			{
				body: {
					newLicenseType,
					additionalDevices,
				},
			},
		);

		// Get existing license
		const existingLicense = await prisma.license.findUnique({
			where: { licenseKey },
		});

		if (!existingLicense) {
			return {
				success: false,
				error: "License not found",
				errorCode: "LICENSE_NOT_FOUND",
			};
		}

		if (existingLicense.licenseType !== "trial") {
			return {
				success: false,
				error: "License is not a trial license",
				errorCode: "NOT_TRIAL_LICENSE",
			};
		}

		// Calculate new max devices
		const baseMaxDevices = newLicenseType === "standard" ? 2 : 5;
		const newMaxDevices = baseMaxDevices + additionalDevices;

		// Update license to paid version
		const updatedLicense = await prisma.license.update({
			where: { licenseKey },
			data: {
				licenseType: newLicenseType,
				maxDevices: newMaxDevices,
				expiresAt: null, // Remove expiration
				stripePaymentIntentId,
				updatedAt: new Date(),
			},
		});

		// Log the conversion
		await prisma.auditLog.create({
			data: {
				action: "LICENSE_CREATED", // Reuse existing action for consistency
				licenseKey,
				details: {
					action: "TRIAL_CONVERTED",
					previousType: "trial",
					newType: newLicenseType,
					previousMaxDevices: existingLicense.maxDevices,
					newMaxDevices,
					stripePaymentIntentId,
				},
			},
		});

		// Send updated license email
		try {
			await sendLicenseEmail(
				existingLicense.email,
				licenseKey,
				newLicenseType,
				null, // No expiration for paid licenses
			);
		} catch (emailError) {
			Logger.error(
				EndpointPrefix.LICENSE_UPGRADE,
				`Failed to send conversion email: ${emailError}`,
				{ body: { error: String(emailError) } },
			);
			// Don't fail the conversion if email fails
		}

		Logger.info(
			EndpointPrefix.LICENSE_UPGRADE,
			`Trial license converted successfully: ${licenseKey}`,
			{
				body: {
					newLicenseType,
					newMaxDevices,
				},
			},
		);

		return {
			success: true,
			updatedLicense,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_UPGRADE,
			`Failed to convert trial license: ${error}`,
			{ body: { error: String(error) } },
		);

		return {
			success: false,
			error: "Failed to convert trial license",
			errorCode: "INTERNAL_ERROR",
		};
	}
}

/**
 * Check if new trial registrations are allowed
 * This function always returns false to prevent new trial registrations
 * while maintaining the infrastructure for existing trials
 */
export function isTrialRegistrationAllowed(): boolean {
	// Always return false to prevent new trial registrations
	// This maintains backward compatibility while stopping new trials
	return false;
}

/**
 * Get trial license information for existing trial users
 * This helps existing trial users understand their license status
 */
export async function getTrialLicenseInfo(email: string): Promise<{
	hasTrialLicense: boolean;
	trialLicense?: License;
	trialStatus?: TrialStatusResult;
}> {
	try {
		const trialLicense = await prisma.license.findFirst({
			where: {
				email: email.toLowerCase(),
				licenseType: "trial",
			},
		});

		if (!trialLicense) {
			return { hasTrialLicense: false };
		}

		const trialStatus = await getTrialStatus(trialLicense.licenseKey);

		return {
			hasTrialLicense: true,
			trialLicense,
			trialStatus,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_STATUS,
			`Failed to get trial license info: ${error}`,
			{ body: { error: String(error) } },
		);

		return { hasTrialLicense: false };
	}
}
