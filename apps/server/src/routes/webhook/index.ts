import express, { Router } from "express";
import stripe, { type Stripe } from "@/lib/stripe";
import {
	EndpointPrefix,
	extractRequestContext,
	Logger,
	requestLoggingMiddleware,
} from "@/utils/logger";

const router: Router = Router();

// Stripe webhook endpoint - handles all Stripe webhook events
router.post(
	"/webhook",
	express.raw({ type: "application/json" }),
	requestLoggingMiddleware(EndpointPrefix.PAYMENT_WEBHOOK),
	async (req, res) => {
		const context = extractRequestContext(req);
		Logger.info(EndpointPrefix.PAYMENT_WEBHOOK, "Processing Stripe webhook", {
			...context,
			body: "[RAW_STRIPE_DATA]",
		});

		const sig = req.headers["stripe-signature"] as string;
		let event: Stripe.Event;

		try {
			event = stripe.webhooks.constructEvent(
				req.body,
				sig,
				process.env.STRIPE_WEBHOOK_SECRET as string,
			);

			Logger.info(
				EndpointPrefix.PAYMENT_WEBHOOK,
				`Webhook event received: ${event.type}`,
				{
					...context,
					body: { eventType: event.type, eventId: event.id },
				},
			);
		} catch (err) {
			const errorMessage = (err as Error).message;
			Logger.error(
				EndpointPrefix.PAYMENT_WEBHOOK,
				`Webhook signature verification failed: ${errorMessage}`,
				context,
			);
			return res.status(400).send(`Webhook Error: ${errorMessage}`);
		}

		// Handle the event
		switch (event.type) {
			case "payment_intent.succeeded":
				await handlePaymentSuccess(event.data.object);
				break;

			case "payment_intent.payment_failed":
				await handlePaymentFailure(event.data.object);
				break;

			case "checkout.session.completed":
				await handleCheckoutSuccess(event.data.object);
				break;

			default:
				console.log(`Unhandled event type ${event.type}`);
		}

		res.json({ received: true });
	},
);

async function handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
	try {
		const metadata = paymentIntent.metadata;

		if (metadata.flow_type === "upgrade") {
			// Handle license upgrade
			const licenseKey = metadata.licenseKey;
			const additionalDevices = Number.parseInt(metadata.additionalDevices, 10);
			// Use service layer for license upgrade
			const { updateLicense } = await import("@/services/license");
			const result = await updateLicense(licenseKey, additionalDevices);

			if (result.success) {
				Logger.info(
					EndpointPrefix.PAYMENT_WEBHOOK,
					`License upgrade processed successfully: ${licenseKey}`,
					{ body: { additionalDevices, paymentIntentId: paymentIntent.id } },
				);
			} else {
				Logger.error(
					EndpointPrefix.PAYMENT_WEBHOOK,
					`License upgrade failed: ${result.error}`,
					{
						body: {
							licenseKey: "[REDACTED]",
							additionalDevices,
							paymentIntentId: paymentIntent.id,
							error: result.error,
						},
					},
				);
			}

			return;
		}

		// Handle new license creation
		const { createLicense } = await import("@/services/license");

		const result = await createLicense({
			email: metadata.email,
			licenseType: metadata.licenseType as "standard" | "extended",
			additionalDevices: Number.parseInt(metadata.additionalDevices, 10) || 0,
			deviceId: metadata.deviceId || undefined,
			stripePaymentIntentId: paymentIntent.id,
		});

		if (result.success) {
			Logger.info(
				EndpointPrefix.PAYMENT_WEBHOOK,
				`Payment processed successfully: ${result.license?.licenseKey || "upgrade"}`,
				{ body: { paymentIntentId: paymentIntent.id } },
			);
		} else {
			Logger.error(
				EndpointPrefix.PAYMENT_WEBHOOK,
				`Payment processing failed: ${result.error}`,
				{ body: { paymentIntentId: paymentIntent.id, error: result.error } },
			);
		}
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Error handling payment success: ${error}`,
			{ body: { paymentIntentId: paymentIntent.id, error: String(error) } },
		);
	}
}

async function handlePaymentFailure(paymentIntent: Stripe.PaymentIntent) {
	try {
		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Payment failed: ${paymentIntent.id}`,
			{
				body: {
					paymentIntentId: paymentIntent.id,
					status: paymentIntent.status,
				},
			},
		);

		// Add any failure handling logic here
		// For example, notify customer, update database, etc.
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Error handling payment failure: ${error}`,
			{ body: { paymentIntentId: paymentIntent.id, error: String(error) } },
		);
	}
}

async function handleCheckoutSuccess(session: Stripe.Checkout.Session) {
	try {
		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Processing checkout session: ${session.id}`,
			{
				body: { sessionId: session.id, paymentStatus: session.payment_status },
			},
		);

		// Access metadata directly from the checkout session (not payment intent)
		const { licenseType, email, additionalDevices, deviceId } =
			session.metadata || {};

		console.log("[WEBHOOK] Checkout session metadata:", session.metadata);
		console.log("[WEBHOOK] Payment intent ID:", session.payment_intent);

		if (!email || !licenseType) {
			Logger.error(
				EndpointPrefix.PAYMENT_WEBHOOK,
				"Missing required metadata in checkout session",
				{ body: { availableMetadata: session.metadata } },
			);
			return;
		}

		// Create license directly using checkout session metadata
		const { createLicense } = await import("@/services/license");

		const result = await createLicense({
			email,
			licenseType: licenseType as "standard" | "extended",
			additionalDevices: Number.parseInt(additionalDevices || "0", 10),
			deviceId: deviceId || undefined,
			stripePaymentIntentId: session.payment_intent as string,
		});

		if (result.success && result.license) {
			Logger.info(
				EndpointPrefix.PAYMENT_WEBHOOK,
				`✅ Checkout completed successfully for ${licenseType} license for ${email}`,
				{ body: { licenseKey: result.license.licenseKey } },
			);
		}
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`❌ Error handling checkout success: ${error}`,
			{ body: { error: String(error), sessionId: session.id } },
		);
	}
}

export default router;
