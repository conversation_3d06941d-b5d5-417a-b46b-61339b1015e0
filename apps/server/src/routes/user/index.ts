/**
 * User management API routes
 * Provides endpoints for user CRUD operations, invitations, and role management
 */

import {
	createUserSchema,
	listUsersSchema,
	sendInvitationSchema,
	updateUserSchema,
} from "@snapback/shared";
import { Router } from "express";
import type { User } from "prisma/generated/client";
import { requireAuth, requireRole } from "@/middleware/auth";
import {
	createUserLimit,
	inviteUserLimit,
	listUsersLimit,
	updateUserLimit,
} from "@/middleware/rate-limit";
import {
	createUser,
	listUsers,
	sendInvitation,
	updateUser,
} from "@/services/user";
import {
	ErrorCode,
	handleDatabaseError,
	handleZodError,
	type PrismaError,
	sendErrorResponse,
} from "@/utils/errors";
import {
	EndpointPrefix,
	extractRequestContext,
	Logger,
	requestLoggingMiddleware,
} from "@/utils/logger";
import { getUserContextWithTrials } from "@/utils/permissions";
import prisma from "../../../prisma";

const router: Router = Router();

/**
 * @route GET /api/users/me
 * @description Get current user information and permissions
 * @access Private
 */
router.get(
	"/me",
	requireAuth,
	requestLoggingMiddleware(EndpointPrefix.USER_LIST),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			const user = req.user as User;

			Logger.info(
				EndpointPrefix.USER_LIST,
				`Getting current user info: ${user.email}`,
				context,
			);

			// Get user context with trial information
			const userContext = await getUserContextWithTrials(user);

			res.status(200).json({
				user: {
					id: user.id,
					name: user.name,
					email: user.email,
					role: user.role,
					isActive: user.isActive,
					emailVerified: user.emailVerified,
					image: user.image,
					lastLoginAt: user.lastLoginAt,
					createdAt: user.createdAt,
					updatedAt: user.updatedAt,
				},
				permissions: userContext.permissions,
				trialInfo: userContext.trialInfo,
			});
		} catch (error) {
			Logger.error(
				EndpointPrefix.USER_LIST,
				`Failed to get current user info: ${error}`,
				{ body: { error: String(error) } },
			);

			sendErrorResponse(
				res,
				"Failed to get user information",
				500,
				ErrorCode.INTERNAL_ERROR,
			);
		}
	},
);

/**
 * @route GET /api/users
 * @description List users with pagination and filtering
 * @access Private - Requires MANAGER role or higher
 */
router.get(
	"/",
	listUsersLimit,
	requireAuth,
	requireRole("MANAGER"),
	requestLoggingMiddleware(EndpointPrefix.USER_LIST),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			const user = req.user as User;

			// Parse query parameters
			const filters = {
				page: Number.parseInt(req.query.page as string) || 1,
				limit: Number.parseInt(req.query.limit as string) || 20,
				role: req.query.role as string,
				isActive: req.query.isActive
					? req.query.isActive === "true"
					: undefined,
				search: req.query.search as string,
			};

			// Validate filters
			const validation = listUsersSchema.safeParse(filters);
			if (!validation.success) {
				return handleZodError(res, validation.error);
			}

			Logger.info(EndpointPrefix.USER_LIST, "Listing users", {
				...context,
				body: { filters: validation.data },
			});

			const result = await listUsers(user, validation.data);

			if (!result.success) {
				return sendErrorResponse(
					res,
					result.error || "Failed to list users",
					result.errorCode === "INSUFFICIENT_PERMISSIONS" ? 403 : 500,
					result.errorCode as ErrorCode,
				);
			}

			res.status(200).json({
				users: result.users,
				pagination: result.pagination,
			});
		} catch (error) {
			Logger.error(EndpointPrefix.USER_LIST, `Failed to list users: ${error}`, {
				body: { error: String(error) },
			});

			if (error && typeof error === "object" && "code" in error) {
				return handleDatabaseError(res, error as PrismaError);
			}

			sendErrorResponse(
				res,
				"Failed to list users",
				500,
				ErrorCode.INTERNAL_ERROR,
			);
		}
	},
);

/**
 * @route POST /api/users
 * @description Create a new user
 * @access Private - Requires ADMIN role or higher
 */
router.post(
	"/",
	createUserLimit,
	requireAuth,
	requireRole("ADMIN"),
	requestLoggingMiddleware(EndpointPrefix.USER_CREATE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			const user = req.user as User;

			// Validate request body
			const validation = createUserSchema.safeParse(req.body);
			if (!validation.success) {
				return handleZodError(res, validation.error);
			}

			Logger.info(
				EndpointPrefix.USER_CREATE,
				`Creating user: ${validation.data.email}`,
				{
					...context,
					body: {
						email: validation.data.email,
						role: validation.data.role,
					},
				},
			);

			const result = await createUser(user, validation.data);

			if (!result.success) {
				const statusCode =
					result.errorCode === "INSUFFICIENT_PERMISSIONS"
						? 403
						: result.errorCode === "USER_ALREADY_EXISTS"
							? 409
							: 500;

				return sendErrorResponse(
					res,
					result.error || "Failed to create user",
					statusCode,
					result.errorCode as ErrorCode,
				);
			}

			res.status(201).json({
				user: {
					id: result.user?.id,
					name: result.user?.name,
					email: result.user?.email,
					role: result.user?.role,
					isActive: result.user?.isActive,
					emailVerified: result.user?.emailVerified,
					createdAt: result.user?.createdAt,
					updatedAt: result.user?.updatedAt,
				},
				message: "User created successfully",
			});
		} catch (error) {
			Logger.error(
				EndpointPrefix.USER_CREATE,
				`Failed to create user: ${error}`,
				{ body: { error: String(error) } },
			);

			if (error && typeof error === "object" && "code" in error) {
				return handleDatabaseError(res, error as PrismaError);
			}

			sendErrorResponse(
				res,
				"Failed to create user",
				500,
				ErrorCode.INTERNAL_ERROR,
			);
		}
	},
);

/**
 * @route PATCH /api/users/:id
 * @description Update user information
 * @access Private - Requires MANAGER role or higher
 */
router.patch(
	"/:id",
	updateUserLimit,
	requireAuth,
	requireRole("MANAGER"),
	requestLoggingMiddleware(EndpointPrefix.USER_UPDATE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			const user = req.user as User;
			const targetUserId = req.params.id;

			// Validate request body
			const validation = updateUserSchema.safeParse(req.body);
			if (!validation.success) {
				return handleZodError(res, validation.error);
			}

			Logger.info(
				EndpointPrefix.USER_UPDATE,
				`Updating user: ${targetUserId}`,
				{
					...context,
					body: {
						targetUserId,
						updateData: validation.data,
					},
				},
			);

			const result = await updateUser(user, targetUserId, validation.data);

			if (!result.success) {
				const statusCode =
					result.errorCode === "INSUFFICIENT_PERMISSIONS"
						? 403
						: result.errorCode === "USER_NOT_FOUND"
							? 404
							: 500;

				return sendErrorResponse(
					res,
					result.error || "Failed to update user",
					statusCode,
					result.errorCode as ErrorCode,
				);
			}

			res.status(200).json({
				user: {
					id: result.user?.id,
					name: result.user?.name,
					email: result.user?.email,
					role: result.user?.role,
					isActive: result.user?.isActive,
					emailVerified: result.user?.emailVerified,
					updatedAt: result.user?.updatedAt,
				},
				message: "User updated successfully",
			});
		} catch (error) {
			Logger.error(
				EndpointPrefix.USER_UPDATE,
				`Failed to update user: ${error}`,
				{ body: { error: String(error) } },
			);

			if (error && typeof error === "object" && "code" in error) {
				return handleDatabaseError(res, error as PrismaError);
			}

			sendErrorResponse(
				res,
				"Failed to update user",
				500,
				ErrorCode.INTERNAL_ERROR,
			);
		}
	},
);

/**
 * @route POST /api/users/invite
 * @description Send user invitation
 * @access Private - Requires MANAGER role or higher
 */
router.post(
	"/invite",
	inviteUserLimit,
	requireAuth,
	requireRole("MANAGER"),
	requestLoggingMiddleware(EndpointPrefix.USER_INVITE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			const user = req.user as User;

			// Validate request body
			const validation = sendInvitationSchema.safeParse(req.body);
			if (!validation.success) {
				return handleZodError(res, validation.error);
			}

			Logger.info(
				EndpointPrefix.USER_INVITE,
				`Sending invitation to: ${validation.data.email}`,
				{
					...context,
					body: {
						email: validation.data.email,
						role: validation.data.role,
					},
				},
			);

			const result = await sendInvitation(user, validation.data);

			if (!result.success) {
				const statusCode =
					result.errorCode === "INSUFFICIENT_PERMISSIONS"
						? 403
						: result.errorCode === "USER_ALREADY_EXISTS"
							? 409
							: result.errorCode === "INVITATION_ALREADY_EXISTS"
								? 409
								: 500;

				return sendErrorResponse(
					res,
					result.error || "Failed to send invitation",
					statusCode,
					result.errorCode as ErrorCode,
				);
			}

			res.status(201).json({
				invitation: {
					id: result.invitation?.id,
					email: result.invitation?.email,
					role: result.invitation?.role,
					status: result.invitation?.status,
					expiresAt: result.invitation?.expiresAt,
					createdAt: result.invitation?.createdAt,
				},
				message: "Invitation sent successfully",
			});
		} catch (error) {
			Logger.error(
				EndpointPrefix.USER_INVITE,
				`Failed to send invitation: ${error}`,
				{ body: { error: String(error) } },
			);

			if (error && typeof error === "object" && "code" in error) {
				return handleDatabaseError(res, error as PrismaError);
			}

			sendErrorResponse(
				res,
				"Failed to send invitation",
				500,
				ErrorCode.INTERNAL_ERROR,
			);
		}
	},
);

/**
 * @route GET /api/users/invitations
 * @description List user invitations with pagination and filtering
 * @access Private - Requires MANAGER role or higher
 */
router.get(
	"/invitations",
	listUsersLimit,
	requireAuth,
	requireRole("MANAGER"),
	requestLoggingMiddleware(EndpointPrefix.USER_LIST),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			const user = req.user as User;

			// Parse query parameters
			const filters = {
				page: Number.parseInt(req.query.page as string) || 1,
				limit: Number.parseInt(req.query.limit as string) || 20,
				status: req.query.status as string,
			};

			Logger.info(EndpointPrefix.USER_LIST, "Listing invitations", {
				...context,
				body: { filters },
			});

			// Get invitations with pagination
			const where: {
				status?: "PENDING" | "ACCEPTED" | "EXPIRED" | "REVOKED";
				invitedById?: string;
			} = {};

			if (
				filters.status &&
				["PENDING", "ACCEPTED", "EXPIRED", "REVOKED"].includes(filters.status)
			) {
				where.status = filters.status as
					| "PENDING"
					| "ACCEPTED"
					| "EXPIRED"
					| "REVOKED";
			}

			// Non-super admins can only see invitations they sent
			if (user.role !== "SUPER_ADMIN") {
				where.invitedById = user.id;
			}

			// Get total count
			const totalCount = await prisma.userInvitation.count({ where });

			// Calculate pagination
			const totalPages = Math.ceil(totalCount / filters.limit);
			const skip = (filters.page - 1) * filters.limit;

			// Get invitations
			const invitations = await prisma.userInvitation.findMany({
				where,
				skip,
				take: filters.limit,
				include: {
					invitedBy: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
				},
				orderBy: [{ createdAt: "desc" }],
			});

			const pagination = {
				page: filters.page,
				limit: filters.limit,
				totalCount,
				totalPages,
				hasNextPage: filters.page < totalPages,
				hasPreviousPage: filters.page > 1,
			};

			res.status(200).json({
				invitations,
				pagination,
			});
		} catch (error) {
			Logger.error(
				EndpointPrefix.USER_LIST,
				`Failed to list invitations: ${error}`,
				{ body: { error: String(error) } },
			);

			if (error && typeof error === "object" && "code" in error) {
				return handleDatabaseError(res, error as PrismaError);
			}

			sendErrorResponse(
				res,
				"Failed to list invitations",
				500,
				ErrorCode.INTERNAL_ERROR,
			);
		}
	},
);

export default router;
