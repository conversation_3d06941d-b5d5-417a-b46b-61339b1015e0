import { Router } from "express";

import {
	createCheckoutSession,
	createPaymentIntent,
	createUpgradePaymentIntent,
	getCheckoutSessionDetails,
	getPaymentIntentDetails,
	getPricingInfo,
} from "@/services/payment";
import {
	EndpointPrefix,
	extractRequestContext,
	Logger,
	requestLoggingMiddleware,
} from "@/utils/logger";

const router: Router = Router();

// Payment configuration

// Create payment intent for license purchase (Embedded Stripe Elements)
router.post(
	"/create-payment-intent",
	requestLoggingMiddleware(EndpointPrefix.PAYMENT_CREATE_INTENT),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.PAYMENT_CREATE_INTENT,
				"Processing payment intent creation",
				context,
			);

			const { licenseType, additionalDevices = 0, email, deviceId } = req.body;

			Logger.info(
				EndpointPrefix.PAYMENT_CREATE_INTENT,
				`Payment intent requested: ${licenseType}`,
				{
					...context,
					body: {
						licenseType,
						additionalDevices,
						email,
						deviceId: deviceId ? "[PROVIDED]" : "[NOT_PROVIDED]",
					},
				},
			);

			// Use service layer to create payment intent
			const result = await createPaymentIntent({
				licenseType,
				additionalDevices,
				email,
				deviceId,
			});

			res.send(result);
		} catch (error) {
			console.error("Error creating payment intent:", error);
			res.status(500).json({ error: "Payment setup failed" });
		}
	},
);

// Create Stripe Checkout Session for redirect-based payments
router.post(
	"/create-checkout-session",
	requestLoggingMiddleware(EndpointPrefix.PAYMENT_CREATE_CHECKOUT),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.PAYMENT_CREATE_CHECKOUT,
				"Processing checkout session creation",
				context,
			);

			const {
				licenseType,
				additionalDevices = 0,
				email,
				deviceId,
				successUrl,
				cancelUrl,
			} = req.body;

			Logger.info(
				EndpointPrefix.PAYMENT_CREATE_CHECKOUT,
				`Checkout session requested: ${licenseType}`,
				{
					...context,
					body: {
						licenseType,
						additionalDevices,
						email,
						deviceId: deviceId ? "[PROVIDED]" : "[NOT_PROVIDED]",
						successUrl,
						cancelUrl,
					},
				},
			);

			if (!successUrl || !cancelUrl) {
				return res
					.status(400)
					.json({ error: "Success and cancel URLs are required" });
			}

			// Use service layer to create checkout session
			const result = await createCheckoutSession({
				licenseType,
				additionalDevices,
				email,
				deviceId,
				successUrl: `${successUrl}?session_id={CHECKOUT_SESSION_ID}`,
				cancelUrl,
			});

			res.json(result);
		} catch (error) {
			console.error("Error creating checkout session:", error);
			res.status(500).json({ error: "Checkout session creation failed" });
		}
	},
);

// Create payment intent for license upgrade
router.post(
	"/create-upgrade-payment-intent",
	requestLoggingMiddleware(EndpointPrefix.PAYMENT_CREATE_INTENT),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.PAYMENT_CREATE_INTENT,
				"Processing upgrade payment intent creation",
				context,
			);

			const { licenseKey, additionalDevices } = req.body;

			Logger.info(
				EndpointPrefix.PAYMENT_CREATE_INTENT,
				"Upgrade payment intent requested",
				{
					...context,
					body: {
						licenseKey: licenseKey
							? `${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}`
							: "[NOT_PROVIDED]",
						additionalDevices,
					},
				},
			);

			if (!licenseKey || !additionalDevices || additionalDevices <= 0) {
				Logger.error(
					EndpointPrefix.PAYMENT_CREATE_INTENT,
					"Invalid upgrade request parameters",
					context,
				);
				return res.status(400).json({ error: "Invalid upgrade request" });
			}

			// Use service layer to create upgrade payment intent
			const result = await createUpgradePaymentIntent(
				licenseKey,
				additionalDevices,
			);

			res.send({
				clientSecret: result.clientSecret,
				amount: result.amount,
				additionalDevices,
			});
		} catch (error) {
			console.error("Error creating upgrade payment intent:", error);
			res.status(500).json({ error: "Upgrade payment setup failed" });
		}
	},
);

// Get pricing information
router.get(
	"/pricing",
	requestLoggingMiddleware(EndpointPrefix.PAYMENT_PRICING),
	(req, res) => {
		const context = extractRequestContext(req);
		Logger.info(
			EndpointPrefix.PAYMENT_PRICING,
			"Pricing information requested",
			context,
		);

		// Use service layer to get pricing info
		const pricingInfo = getPricingInfo();
		res.json(pricingInfo);
	},
);

// Get checkout session status (for redirect flow)
router.get(
	"/checkout-session/:sessionId",
	requestLoggingMiddleware(EndpointPrefix.PAYMENT_STATUS),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.PAYMENT_STATUS,
				"Processing checkout session status request",
				context,
			);

			const { sessionId } = req.params;

			if (!sessionId) {
				return res.status(400).json({ error: "Session ID is required" });
			}

			Logger.info(
				EndpointPrefix.PAYMENT_STATUS,
				"Checkout session status requested",
				{
					...context,
					params: { sessionId: "[PROVIDED]" },
				},
			);

			// Use service layer to get checkout session details
			const result = await getCheckoutSessionDetails(sessionId);

			if (!result.success) {
				const statusCode = result.error === "Session not found" ? 404 : 500;
				return res.status(statusCode).json({ error: result.error });
			}

			res.json(result.data);
		} catch (error) {
			Logger.error(
				EndpointPrefix.PAYMENT_STATUS,
				`Error in checkout session route: ${error}`,
				{ body: { sessionId: req.params.sessionId, error: String(error) } },
			);
			res.status(500).json({ error: "Failed to retrieve session" });
		}
	},
);

// Get payment intent status (for embedded flow)
router.get(
	"/payment-intent/:paymentIntentId",
	requestLoggingMiddleware(EndpointPrefix.PAYMENT_STATUS),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.PAYMENT_STATUS,
				"Processing payment intent status request",
				context,
			);

			const { paymentIntentId } = req.params;

			if (!paymentIntentId) {
				return res.status(400).json({ error: "Payment intent ID is required" });
			}

			Logger.info(
				EndpointPrefix.PAYMENT_STATUS,
				"Payment intent status requested",
				{
					...context,
					params: {
						paymentIntentId: "[PROVIDED]",
					},
				},
			);

			// Use service layer to get payment intent details
			const result = await getPaymentIntentDetails(paymentIntentId);

			if (!result.success) {
				const statusCode =
					result.error === "Payment intent not found" ? 404 : 500;
				return res.status(statusCode).json({ error: result.error });
			}

			res.json(result.data);
		} catch (error) {
			Logger.error(
				EndpointPrefix.PAYMENT_STATUS,
				`Error in payment intent route: ${error}`,
				{
					body: {
						paymentIntentId: req.params.paymentIntentId,
						error: String(error),
					},
				},
			);
			res.status(500).json({ error: "Failed to retrieve payment intent" });
		}
	},
);

export default router;
