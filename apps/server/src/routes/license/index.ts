// Removed createId import - now using generateLicenseKey from utils

import {
	createLicenseSchema,
	listLicensesSchema,
	resendLicenseSchema,
	updateDeviceMetadataSchema,
	validateLicenseSchema,
} from "@snapback/shared";
import { Router } from "express";
import type { Device, License, Prisma } from "prisma/generated/client";
import z from "zod";
import stripe from "@/lib/stripe";
import {
	createLicenseLimit,
	resendLicenseLimit,
	validateLicenseLimit,
} from "@/middleware/rate-limit";
import {
	convertTrialToPaid,
	createLicense,
	getTrialLicenseInfo,
	getTrialStatus,
	isTrialRegistrationAllowed,
	resendLicenseEmail,
	validateLicense,
} from "@/services/license";

import { hashDeviceId, verifyDeviceToken } from "@/utils";
import {
	ErrorCode,
	handleDatabaseError,
	handleStripeError,
	handleZodError,
	LicenseErrors,
	type PrismaError,
	type StripeError,
	sendErrorResponse,
} from "@/utils/errors";
import type { LogContext } from "@/utils/logger";
import {
	EndpointPrefix,
	extractRequestContext,
	Logger,
	requestLoggingMiddleware,
} from "@/utils/logger";
import {
	calculatePagination,
	extractPaginationFromQuery,
} from "@/utils/pagination";
import prisma from "../../../prisma";

// Advanced type definitions for API responses
type LicenseWithDevices = License & { devices: Device[] };
type ApiResponseData =
	| { license: LicenseWithDevices }
	| { licenses: License[] }
	| { devices: Device[] }
	| { message: string }
	| { error: string }
	| Record<string, unknown>
	| unknown;

// Type for sanitized response data
type SanitizedResponseData = Record<string, unknown>;

/**
 * Sanitize response data for logging by removing or masking sensitive information
 * Uses advanced TypeScript generics to maintain type safety while sanitizing
 */
function sanitizeResponseForLogging<T extends ApiResponseData>(
	responseData: T,
): SanitizedResponseData {
	if (!responseData || typeof responseData !== "object") {
		return responseData as SanitizedResponseData;
	}

	const sanitized = { ...responseData } as Record<string, unknown>;

	// Mask license keys for security (show first 4 and last 4 characters)
	if (sanitized.licenseKey && typeof sanitized.licenseKey === "string") {
		const key = sanitized.licenseKey;
		if (key.length > 8) {
			sanitized.licenseKey = `${key.substring(0, 4)}****${key.substring(key.length - 4)}`;
		} else {
			sanitized.licenseKey = "****";
		}
	}

	// Sanitize nested objects
	if (sanitized.license && typeof sanitized.license === "object") {
		sanitized.license = sanitizeResponseForLogging(
			sanitized.license as ApiResponseData,
		);
	}

	// Sanitize arrays of licenses or devices using proper typing
	if (Array.isArray(sanitized.licenses)) {
		sanitized.licenses = sanitized.licenses.map((license: unknown) =>
			sanitizeResponseForLogging(license as ApiResponseData),
		);
	}
	if (Array.isArray(sanitized.devices)) {
		sanitized.devices = sanitized.devices.map((device: unknown) =>
			sanitizeResponseForLogging(device as ApiResponseData),
		);
	}

	return sanitized;
}

/**
 * Log API response for debugging purposes
 */
export function logApiResponse(
	endpointPrefix: EndpointPrefix,
	statusCode: number,
	responseData: ApiResponseData,
	context: Partial<LogContext>,
	additionalInfo?: string,
) {
	const sanitizedResponse = sanitizeResponseForLogging(responseData);
	const logLevel = statusCode >= 400 ? "error" : "info";
	const logMessage = `API Response ${statusCode}${additionalInfo ? ` - ${additionalInfo}` : ""}`;

	// Create extended context with response data
	const extendedContext = {
		...context,
		statusCode,
		responseData: sanitizedResponse,
	} as Partial<LogContext> & { responseData: SanitizedResponseData };

	if (logLevel === "error") {
		Logger.error(endpointPrefix, logMessage, extendedContext);
	} else {
		Logger.info(endpointPrefix, logMessage, extendedContext);
	}
}

const router: Router = Router();

/**
 * @route GET /api/licenses
 * @description List licenses with pagination and filtering
 * @access Private - Requires ADMIN role or higher
 * @rateLimit General API rate limit
 *
 * @param {number} [req.query.page] - Page number for pagination (default: 1)
 * @param {number} [req.query.limit] - Number of results per page (default: 20, max: 100)
 * @param {string} [req.query.licenseType] - Filter by license type (trial, standard, extended)
 * @param {boolean} [req.query.isActive] - Filter by active status
 * @param {boolean} [req.query.isExpired] - Filter by expiration status
 * @param {string} [req.query.search] - Search by email or license key
 * @param {string} [req.query.email] - Filter by specific email
 *
 * @returns {Object} 200 - Licenses retrieved successfully
 * @returns {Array} returns.licenses - Array of license objects
 * @returns {Object} returns.pagination - Pagination metadata
 *
 * @returns {Object} 400 - Validation error
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions
 * @returns {Object} 500 - Internal server error
 */
router.get(
	"/",
	requestLoggingMiddleware(EndpointPrefix.LICENSE_STATUS),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);

			// Parse and validate query parameters
			const filters = {
				page: Number.parseInt(req.query.page as string) || 1,
				limit: Number.parseInt(req.query.limit as string) || 20,
				licenseType: req.query.licenseType as string,
				isActive: req.query.isActive
					? req.query.isActive === "true"
					: undefined,
				isExpired: req.query.isExpired
					? req.query.isExpired === "true"
					: undefined,
				search: req.query.search as string,
				email: req.query.email as string,
			};

			// Validate filters using schema
			const validation = listLicensesSchema.safeParse(filters);
			if (!validation.success) {
				return handleZodError(res, validation.error);
			}

			Logger.info(EndpointPrefix.LICENSE_STATUS, "Listing licenses", {
				...context,
				body: { filters: validation.data },
			});

			const validatedFilters = validation.data;
			const pagination = calculatePagination(validatedFilters);

			// Build where clause for filtering
			const where: Prisma.LicenseWhereInput = {};

			if (validatedFilters.licenseType) {
				where.licenseType = validatedFilters.licenseType;
			}

			if (validatedFilters.isActive !== undefined) {
				// Active means not refunded and not expired
				if (validatedFilters.isActive) {
					where.refundedAt = null;
				} else {
					where.refundedAt = { not: null };
				}
			}

			if (validatedFilters.isExpired !== undefined) {
				const now = new Date();
				if (validatedFilters.isExpired) {
					where.expiresAt = { lt: now };
				} else {
					where.OR = [
						{ expiresAt: null }, // Lifetime licenses
						{ expiresAt: { gte: now } }, // Not expired
					];
				}
			}

			if (validatedFilters.email) {
				where.email = { contains: validatedFilters.email, mode: "insensitive" };
			}

			if (validatedFilters.search) {
				where.OR = [
					{ email: { contains: validatedFilters.search, mode: "insensitive" } },
					{
						licenseKey: {
							contains: validatedFilters.search,
							mode: "insensitive",
						},
					},
				];
			}

			// Get total count and licenses
			const [totalCount, licenses] = await Promise.all([
				prisma.license.count({ where }),
				prisma.license.findMany({
					where,
					skip: pagination.skip,
					take: pagination.take,
					orderBy: [{ createdAt: "desc" }],
					include: {
						devices: {
							select: {
								id: true,
								isActive: true,
								deviceType: true,
								lastSeen: true,
							},
						},
					},
				}),
			]);

			// Format response
			const responseData = {
				licenses: licenses.map((license) => ({
					id: license.id,
					licenseKey: license.licenseKey,
					email: license.email,
					licenseType: license.licenseType,
					maxDevices: license.maxDevices,
					devicesUsed: license.devices.length,
					isActive: !license.refundedAt,
					isExpired: license.expiresAt ? license.expiresAt < new Date() : false,
					createdAt: license.createdAt,
					expiresAt: license.expiresAt,
					refundedAt: license.refundedAt,
					devices: license.devices,
				})),
				pagination: pagination.meta(totalCount),
			};

			res.status(200).json(responseData);

			Logger.info(
				EndpointPrefix.LICENSE_STATUS,
				"Licenses listed successfully",
				{
					...context,
					body: {
						count: licenses.length,
						totalCount,
						page: validatedFilters.page,
						limit: validatedFilters.limit,
					},
				},
			);
		} catch (error) {
			Logger.error(
				EndpointPrefix.LICENSE_STATUS,
				`Failed to list licenses: ${error}`,
				{
					body: { error: String(error) },
				},
			);

			return sendErrorResponse(
				res,
				"Failed to retrieve licenses",
				500,
				ErrorCode.INTERNAL_ERROR,
			);
		}
	},
);

/**
 * @route POST /api/licenses/create
 * @description Create a new paid license (standard or extended)
 * @access Public
 * @rateLimit 5 requests per 15 minutes per IP
 *
 * @param {Object} req.body - Request body
 * @param {string} req.body.email - User's email address
 * @param {string} req.body.licenseType - Type of license (standard, extended)
 * @param {string} req.body.stripePaymentIntentId - Stripe payment intent ID (required for all licenses)
 *
 * @returns {Object} 201 - License created successfully
 * @returns {string} returns.message - Success message
 * @returns {Object} returns.license - Complete license object
 * @returns {string} returns.license.licenseKey - Generated license key
 * @returns {string} returns.license.licenseType - Type of license created
 * @returns {Date} returns.license.expiresAt - License expiration date (null for lifetime licenses)
 * @returns {number} returns.license.maxDevices - Maximum number of devices allowed
 * @returns {Object} returns.deliveryInfo - Email delivery information
 *
 * @returns {Object} 400 - Validation error or payment incomplete
 * @returns {Object} 409 - License already exists for email
 * @returns {Object} 429 - Rate limit exceeded
 * @returns {Object} 500 - Internal server error
 *
 * @example
 * // Create paid license
 * POST /api/licenses/create
 * {
 *   "email": "<EMAIL>",
 *   "licenseType": "standard",
 *   "stripePaymentIntentId": "pi_1234567890"
 * }
 */
router.post(
	"/create",
	createLicenseLimit,
	requestLoggingMiddleware(EndpointPrefix.LICENSE_CREATE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.LICENSE_CREATE,
				"Processing license creation request",
				context,
			);

			const { email, licenseType, stripePaymentIntentId } =
				createLicenseSchema.parse(req.body);

			// Prevent new trial registrations
			if (licenseType === "trial" && !isTrialRegistrationAllowed()) {
				Logger.info(
					EndpointPrefix.LICENSE_CREATE,
					`Trial registration blocked for: ${email}`,
					{
						...context,
						body: { email, licenseType },
					},
				);

				return sendErrorResponse(
					res,
					"Trial registrations no longer available",
					400,
					ErrorCode.VALIDATION_ERROR,
					"Free trials are no longer available for new users. Please purchase a license directly. Existing trial users can continue using their current licenses.",
				);
			}

			Logger.info(
				EndpointPrefix.LICENSE_CREATE,
				`License creation requested: ${licenseType} for ${email}`,
				{
					...context,
					body: {
						email,
						licenseType,
						stripePaymentIntentId: stripePaymentIntentId
							? "[PROVIDED]"
							: "[NOT_PROVIDED]",
					},
				},
			);

			// Use service layer to create license
			const result = await createLicense({
				email,
				licenseType,
				stripePaymentIntentId,
			});

			if (!result.success) {
				Logger.error(
					EndpointPrefix.LICENSE_CREATE,
					`License creation failed: ${result.error}`,
					context,
				);
				return sendErrorResponse(
					res,
					"Failed to create license",
					500,
					ErrorCode.INTERNAL_ERROR,
					result.error || "Unknown error occurred",
				);
			}

			const license = result.license as License;

			// Log license creation
			await prisma.auditLog.create({
				data: {
					action: "LICENSE_CREATED",
					details: {
						licenseKey: license.licenseKey,
						licenseType,
						email,
						maxDevices: license.maxDevices,
						expiresAt: license.expiresAt,
						stripePaymentIntentId,
					},
				},
			});

			const responseMessage =
				"License created successfully. The license key has been sent to your email address.";

			// Fetch the complete license object with all fields for response
			const completeLicense = await prisma.license.findUnique({
				where: { id: license.id },
				include: {
					devices: {
						select: {
							id: true,
							deviceHash: false, // Don't expose sensitive hash
							salt: false, // Don't expose salt
							firstSeen: true,
							lastSeen: true,
							appVersion: true,
							isActive: true,
						},
					},
				},
			});

			if (!completeLicense) {
				throw new Error("Failed to retrieve created license");
			}

			// Prepare complete license response object
			const responseData = {
				message: responseMessage,
				license: {
					id: completeLicense.id,
					licenseKey: completeLicense.licenseKey,
					email: completeLicense.email,
					licenseType: completeLicense.licenseType,
					maxDevices: completeLicense.maxDevices,
					expiresAt: completeLicense.expiresAt,
					createdAt: completeLicense.createdAt,
					updatedAt: completeLicense.updatedAt,
					stripePaymentIntentId: completeLicense.stripePaymentIntentId,
					devicesUsed: completeLicense.devices.length,
					devices: completeLicense.devices,
				},
				deliveryInfo: {
					emailSent: true,
					note: "License key has been sent to your email address. Please check your inbox.",
				},
			};

			// Log the response for debugging
			logApiResponse(
				EndpointPrefix.LICENSE_CREATE,
				201,
				responseData,
				context,
				`License created: ${licenseType} for ${email}`,
			);

			res.status(201).json(responseData);
		} catch (error) {
			console.error("Error creating license:", error);

			if (error instanceof z.ZodError) {
				return handleZodError(res, error);
			}

			// Handle database errors
			if (
				error &&
				typeof error === "object" &&
				"code" in error &&
				typeof error.code === "string" &&
				error.code.startsWith("P") &&
				"name" in error &&
				"message" in error
			) {
				return handleDatabaseError(res, error as PrismaError);
			}

			// Handle Stripe errors
			if (
				error &&
				typeof error === "object" &&
				"type" in error &&
				typeof error.type === "string" &&
				error.type.startsWith("Stripe") &&
				"name" in error &&
				"message" in error
			) {
				return handleStripeError(res, error as StripeError);
			}

			sendErrorResponse(
				res,
				"Internal server error",
				500,
				ErrorCode.INTERNAL_ERROR,
				"An unexpected error occurred while creating the license",
			);
		}
	},
);

/**
 * @route POST /api/licenses/validate
 * @description Validate a license key and register/authenticate a device
 * @access Public
 * @rateLimit 30 requests per minute per IP
 *
 * @param {Object} req.body - Request body
 * @param {string} req.body.licenseKey - License key to validate
 * @param {string} req.body.deviceId - Unique device identifier
 * @param {string} req.body.appVersion - Application version making the request
 *
 * @returns {Object} 200 - License validation successful
 * @returns {boolean} returns.valid - Whether the license is valid
 * @returns {string} returns.licenseType - Type of license
 * @returns {Date} returns.expiresAt - License expiration date (null for lifetime)
 * @returns {number} returns.maxDevices - Maximum devices allowed
 * @returns {number} returns.devicesUsed - Current number of registered devices
 * @returns {string} returns.deviceToken - JWT token for device authentication
 * @returns {boolean} returns.isNewDevice - Whether this is a newly registered device
 *
 * @returns {Object} 400 - Validation error
 * @returns {Object} 403 - License expired or max devices reached
 * @returns {Object} 404 - License not found
 * @returns {Object} 429 - Rate limit exceeded
 * @returns {Object} 500 - Internal server error
 *
 * @example
 * POST /api/licenses/validate
 * {
 *   "licenseKey": "clx1234567890abcdef",
 *   "deviceId": "abc123def456",
 *   "appVersion": "1.0.0"
 * }
 */
router.post(
	"/validate",
	validateLicenseLimit,
	requestLoggingMiddleware(EndpointPrefix.LICENSE_VALIDATE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.LICENSE_VALIDATE,
				"Processing license validation request",
				context,
			);

			const { licenseKey, deviceId, appVersion, deviceMetadata } =
				validateLicenseSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.LICENSE_VALIDATE,
				"License validation requested",
				{
					...context,
					body: {
						licenseKey: licenseKey
							? `${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}`
							: "[NOT_PROVIDED]",
						deviceId: deviceId ? "[PROVIDED]" : "[NOT_PROVIDED]",
						appVersion,
					},
				},
			);

			// Use service layer to validate license
			const result = await validateLicense({
				licenseKey,
				deviceId,
				appVersion,
				deviceMetadata,
			});

			if (!result.success) {
				const maskedLicenseKey = `${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}`;

				// Log error response for debugging
				logApiResponse(
					EndpointPrefix.LICENSE_VALIDATE,
					result.errorCode === "LICENSE_NOT_FOUND" ? 404 : 403,
					{ error: result.error, code: result.errorCode },
					context,
					`License validation failed: ${maskedLicenseKey} - ${result.error}`,
				);

				// Return appropriate error response
				switch (result.errorCode) {
					case "LICENSE_NOT_FOUND":
						return LicenseErrors.notFound(res);
					case "LICENSE_EXPIRED":
						return LicenseErrors.expired(res);
					case "LICENSE_REFUNDED":
						return LicenseErrors.refunded(res, new Date()); // Service doesn't return refund date
					case "DEVICE_LIMIT_EXCEEDED":
						return LicenseErrors.maxDevicesReached(res, 0, 0); // Service handles the logic
					default:
						return sendErrorResponse(
							res,
							result.error || "License validation failed",
							500,
							ErrorCode.INTERNAL_ERROR,
							"An unexpected error occurred during license validation",
						);
				}
			}

			const license = result.license;
			const deviceToken = result.deviceToken;

			if (!license || !deviceToken) {
				return sendErrorResponse(
					res,
					"License validation succeeded but missing data",
					500,
					ErrorCode.INTERNAL_ERROR,
					"License or device token is missing from service response",
				);
			}

			// Create response data
			const responseData = {
				valid: true,
				deviceToken,
				licenseType: license.licenseType,
				expiresAt: license.expiresAt,
				maxDevices: license.maxDevices,
				devicesUsed: license.devices.length,
			};

			// Log the response for debugging
			logApiResponse(
				EndpointPrefix.LICENSE_VALIDATE,
				200,
				responseData,
				context,
				"License validated successfully",
			);

			res.json(responseData);
		} catch (error) {
			console.error("Error validating license:", error);

			if (error instanceof z.ZodError) {
				return res
					.status(400)
					.json({ error: "Invalid input data", details: error.message });
			}

			res.status(500).json({ error: "Internal server error" });
		}
	},
);

/**
 * @route POST /api/licenses/resend
 * @description Resend license information to user's email address
 * @access Public
 * @rateLimit 3 requests per hour per IP
 *
 * @param {Object} req.body - Request body
 * @param {string} req.body.email - Email address to resend license to
 *
 * @returns {Object} 200 - Success response (always returns success for security)
 * @returns {string} returns.message - Confirmation message
 *
 * @returns {Object} 400 - Validation error
 * @returns {Object} 429 - Rate limit exceeded
 * @returns {Object} 500 - Internal server error
 *
 * @example
 * POST /api/licenses/resend
 * {
 *   "email": "<EMAIL>"
 * }
 *
 * @security This endpoint always returns success to prevent email enumeration attacks
 */
router.post(
	"/resend",
	resendLicenseLimit,
	requestLoggingMiddleware(EndpointPrefix.LICENSE_RESEND),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.LICENSE_RESEND,
				"Processing license resend request",
				context,
			);

			const { email } = resendLicenseSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.LICENSE_RESEND,
				`License resend requested for: ${email}`,
				{
					...context,
					body: { email },
				},
			);

			// Use service layer to resend license
			const result = await resendLicenseEmail(email);

			// Always return success message for security (don't reveal if email exists)
			const responseData = {
				message: "If a license exists for this email, it has been sent.",
			};

			// Log the response for debugging
			logApiResponse(
				EndpointPrefix.LICENSE_RESEND,
				200,
				responseData,
				context,
				result.success
					? `License resent successfully (${result.licensesSent} licenses sent)`
					: "License resend - no active licenses found for email",
			);

			res.json(responseData);
		} catch (error) {
			console.error("Error resending license:", error);

			if (error instanceof z.ZodError) {
				return res
					.status(400)
					.json({ error: "Invalid input data", details: error.message });
			}

			res.status(500).json({ error: "Internal server error" });
		}
	},
);

// Upgrade license (add more devices)
router.post(
	"/upgrade",
	createLicenseLimit,
	requestLoggingMiddleware(EndpointPrefix.LICENSE_UPGRADE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.LICENSE_UPGRADE,
				"Processing license upgrade request",
				context,
			);

			const { licenseKey, additionalDevices, stripePaymentIntentId } = req.body;

			Logger.info(EndpointPrefix.LICENSE_UPGRADE, "License upgrade requested", {
				...context,
				body: {
					licenseKey: licenseKey
						? `${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}`
						: "[NOT_PROVIDED]",
					additionalDevices,
					stripePaymentIntentId: stripePaymentIntentId
						? "[PROVIDED]"
						: "[NOT_PROVIDED]",
				},
			});

			// Verify payment
			if (stripePaymentIntentId) {
				const paymentIntent = await stripe.paymentIntents.retrieve(
					stripePaymentIntentId,
				);
				if (paymentIntent.status !== "succeeded") {
					return res.status(400).json({ error: "Payment not completed" });
				}
			}

			const license = await prisma.license.findUnique({
				where: { licenseKey },
			});

			if (!license) {
				return res.status(404).json({ error: "License not found" });
			}

			// Update max devices
			await prisma.license.update({
				where: { licenseKey },
				data: {
					maxDevices: license.maxDevices + additionalDevices,
					upgradePaymentIntentId: stripePaymentIntentId,
				},
			});

			res.json({
				message: "License upgraded successfully",
				newMaxDevices: license.maxDevices + additionalDevices,
			});
		} catch (error) {
			console.error("Error upgrading license:", error);
			res.status(500).json({ error: "Internal server error" });
		}
	},
);

/**
 * @route GET /api/licenses/status/:licenseKey
 * @description Get detailed status information for a license
 * @access Public
 * @rateLimit General API rate limit applies
 *
 * @param {string} req.params.licenseKey - License key to check status for
 *
 * @returns {Object} 200 - License status information
 * @returns {string} returns.licenseKey - The license key
 * @returns {string} returns.licenseType - Type of license
 * @returns {string} returns.email - Associated email address
 * @returns {Date} returns.createdAt - License creation date
 * @returns {Date} returns.expiresAt - License expiration date (null for lifetime)
 * @returns {number} returns.maxDevices - Maximum devices allowed
 * @returns {number} returns.devicesUsed - Current number of registered devices
 * @returns {boolean} returns.isExpired - Whether the license has expired
 * @returns {boolean} returns.isActive - Whether the license is active
 * @returns {Array} returns.devices - Array of registered devices (without sensitive data)
 *
 * @returns {Object} 404 - License not found
 * @returns {Object} 500 - Internal server error
 *
 * @example
 * GET /api/licenses/status/clx1234567890abcdef
 */
router.get(
	"/status/:licenseKey",
	requestLoggingMiddleware(EndpointPrefix.LICENSE_STATUS),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.LICENSE_STATUS,
				"Processing license status request",
				context,
			);

			const { licenseKey } = req.params;

			Logger.info(EndpointPrefix.LICENSE_STATUS, "License status requested", {
				...context,
				params: {
					licenseKey: licenseKey
						? `${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}`
						: "[NOT_PROVIDED]",
				},
			});

			const license = await prisma.license.findUnique({
				where: { licenseKey },
				include: {
					devices: {
						select: {
							id: true,
							firstSeen: true,
							lastSeen: true,
							appVersion: true,
							isActive: true,
							deviceType: true,
							deviceModel: true,
							operatingSystem: true,
							architecture: true,
							screenResolution: true,
							totalMemory: true,

							// Exclude sensitive deviceHash and salt
						},
					},
				},
			});

			if (!license) {
				// Log error response for debugging
				logApiResponse(
					EndpointPrefix.LICENSE_STATUS,
					404,
					{ error: "License not found", code: "LICENSE_NOT_FOUND" },
					context,
					`License not found: ${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}`,
				);
				return LicenseErrors.notFound(res);
			}

			const isExpired = license.expiresAt
				? license.expiresAt < new Date()
				: false;
			const isActive = !isExpired;

			const responseData = {
				licenseKey: license.licenseKey,
				licenseType: license.licenseType,
				email: license.email,
				createdAt: license.createdAt,
				expiresAt: license.expiresAt,
				maxDevices: license.maxDevices,
				devicesUsed: license.devices.length,
				isExpired,
				isActive,
				devices: license.devices.map((device) => ({
					id: device.id,
					firstSeen: device.firstSeen,
					lastSeen: device.lastSeen,
					appVersion: device.appVersion,
					isActive: device.isActive,
					deviceType: device.deviceType,
					deviceModel: device.deviceModel,
					operatingSystem: device.operatingSystem,
					architecture: device.architecture,
					screenResolution: device.screenResolution,
					totalMemory: device.totalMemory,
				})),
			};

			// Log the response for debugging
			logApiResponse(
				EndpointPrefix.LICENSE_STATUS,
				200,
				responseData,
				context,
				"License status retrieved",
			);

			res.json(responseData);
		} catch (error) {
			console.error("Error getting license status:", error);

			sendErrorResponse(
				res,
				"Internal server error",
				500,
				ErrorCode.INTERNAL_ERROR,
				"An unexpected error occurred while retrieving license status",
			);
		}
	},
);

/**
 * @route DELETE /api/licenses/devices/:deviceId
 * @description Remove a device from a license
 * @access Public (requires device token authentication)
 * @rateLimit General API rate limit applies
 *
 * @param {string} req.params.deviceId - Device ID to remove
 * @param {string} req.headers.authorization - Bearer token with device JWT
 *
 * @returns {Object} 200 - Device removed successfully
 * @returns {string} returns.message - Success message
 * @returns {number} returns.devicesRemaining - Number of devices remaining on license
 *
 * @returns {Object} 401 - Invalid or missing device token
 * @returns {Object} 404 - Device not found
 * @returns {Object} 500 - Internal server error
 *
 * @example
 * DELETE /api/licenses/devices/abc123def456
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 */
router.delete(
	"/devices/:deviceId",
	requestLoggingMiddleware(EndpointPrefix.LICENSE_REMOVE_DEVICE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.LICENSE_REMOVE_DEVICE,
				"Processing device removal request",
				context,
			);

			const { deviceId } = req.params;
			const authHeader = req.headers.authorization;

			Logger.info(
				EndpointPrefix.LICENSE_REMOVE_DEVICE,
				"Device removal requested",
				{
					...context,
					params: { deviceId: deviceId ? "[PROVIDED]" : "[NOT_PROVIDED]" },
					authorization: authHeader ? "[PROVIDED]" : "[NOT_PROVIDED]",
				},
			);

			if (!authHeader || !authHeader.startsWith("Bearer ")) {
				return sendErrorResponse(
					res,
					"Missing or invalid authorization header",
					401,
					ErrorCode.UNAUTHORIZED,
					"A valid device token is required",
				);
			}

			const token = authHeader.substring(7);
			const payload = verifyDeviceToken(token) as {
				licenseId: string;
				deviceHash: string;
			} | null;

			if (!payload) {
				return sendErrorResponse(
					res,
					"Invalid device token",
					401,
					ErrorCode.INVALID_TOKEN,
					"The provided device token is invalid or expired",
				);
			}

			// Find the device by trying all possible salts for this license
			const license = await prisma.license.findUnique({
				where: { id: payload.licenseId },
				include: { devices: true },
			});

			if (!license) {
				return sendErrorResponse(
					res,
					"License not found",
					404,
					ErrorCode.NOT_FOUND,
					"The license associated with this device token was not found",
				);
			}

			let deviceToRemove = null;
			for (const device of license.devices) {
				const { hash: testHash } = hashDeviceId(deviceId, device.salt);
				if (testHash === device.deviceHash) {
					deviceToRemove = device;
					break;
				}
			}

			if (!deviceToRemove) {
				return sendErrorResponse(
					res,
					"Device not found",
					404,
					ErrorCode.DEVICE_NOT_REGISTERED,
					"The specified device is not registered with this license",
				);
			}

			// Remove the device
			await prisma.device.delete({
				where: { id: deviceToRemove.id },
			});

			// Log the device removal
			await prisma.auditLog.create({
				data: {
					action: "DEVICE_REMOVED",
					details: {
						licenseId: license.id,
						deviceId: deviceToRemove.id,
						licenseKey: license.licenseKey,
					},
				},
			});

			const responseData = {
				message: "Device removed successfully",
				devicesRemaining: license.devices.length - 1,
			};

			// Log the response for debugging
			logApiResponse(
				EndpointPrefix.LICENSE_REMOVE_DEVICE,
				200,
				responseData,
				context,
				"Device removed successfully",
			);

			res.json(responseData);
		} catch (error) {
			console.error("Error removing device:", error);

			if (
				error &&
				typeof error === "object" &&
				"code" in error &&
				typeof error.code === "string" &&
				error.code.startsWith("P") &&
				"name" in error &&
				"message" in error
			) {
				return handleDatabaseError(res, error as PrismaError);
			}

			sendErrorResponse(
				res,
				"Internal server error",
				500,
				ErrorCode.INTERNAL_ERROR,
				"An unexpected error occurred while removing the device",
			);
		}
	},
);

/**
 * @route PUT /api/licenses/devices/metadata
 * @description Update device metadata for better user experience
 * @access Authenticated (Bearer token required)
 *
 * @param {Object} req.body - Request body
 * @param {string} req.body.deviceId - Device identifier
 * @param {Object} req.body.deviceMetadata - Device metadata to update
 * @param {string} [req.body.deviceMetadata.deviceName] - User-friendly device name
 * @param {string} [req.body.deviceMetadata.deviceType] - Device type (e.g., "MacBook Pro")
 * @param {string} [req.body.deviceMetadata.deviceModel] - Model details (e.g., "14-inch M2 2023")
 * @param {string} [req.body.deviceMetadata.operatingSystem] - OS version
 * @param {string} [req.body.deviceMetadata.architecture] - CPU architecture
 * @param {string} [req.body.deviceMetadata.screenResolution] - Display resolution
 * @param {string} [req.body.deviceMetadata.totalMemory] - RAM amount
 * @param {string} [req.body.deviceMetadata.userNickname] - Custom device nickname
 * @param {string} [req.body.deviceMetadata.location] - Device location
 * @param {string} [req.body.deviceMetadata.notes] - User notes about device
 *
 * @returns {Object} 200 - Device metadata updated successfully
 * @returns {string} returns.message - Success message
 * @returns {Object} returns.device - Updated device information
 *
 * @returns {Object} 400 - Validation error
 * @returns {Object} 401 - Invalid or missing device token
 * @returns {Object} 404 - Device not found
 * @returns {Object} 500 - Internal server error
 *
 * @example
 * PUT /api/licenses/devices/metadata
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * {
 *   "deviceId": "abc123def456",
 *   "deviceMetadata": {
 *     "deviceName": "John's MacBook Pro",
 *     "deviceType": "MacBook Pro",
 *     "deviceModel": "14-inch M2 2023",
 *     "userNickname": "Work Laptop",
 *     "location": "Office"
 *   }
 * }
 */
router.put(
	"/devices/metadata",
	requestLoggingMiddleware(EndpointPrefix.LICENSE_UPDATE_DEVICE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.LICENSE_UPDATE_DEVICE,
				"Processing device metadata update request",
				context,
			);

			// Verify device token
			const authHeader = req.headers.authorization;
			if (!authHeader || !authHeader.startsWith("Bearer ")) {
				return res
					.status(401)
					.json({ error: "Missing or invalid authorization header" });
			}

			const token = authHeader.substring(7);
			const payload = verifyDeviceToken(token) as {
				licenseId: string;
				deviceHash: string;
			} | null;
			if (!payload) {
				return res.status(401).json({ error: "Invalid device token" });
			}

			const { deviceId, deviceMetadata } = updateDeviceMetadataSchema.parse(
				req.body,
			);

			Logger.info(
				EndpointPrefix.LICENSE_UPDATE_DEVICE,
				"Device metadata update requested",
				{
					...context,
					body: {
						deviceId: deviceId ? "[PROVIDED]" : "[NOT_PROVIDED]",
						deviceMetadata: deviceMetadata ? "[PROVIDED]" : "[NOT_PROVIDED]",
					},
				},
			);

			// Find the device by hashing the provided deviceId with all possible salts
			const license = await prisma.license.findUnique({
				where: { id: payload.licenseId },
				include: { devices: true },
			});

			if (!license) {
				return res.status(404).json({ error: "License not found" });
			}

			let targetDevice = null;
			for (const device of license.devices) {
				const { hash: testHash } = hashDeviceId(deviceId, device.salt);
				if (testHash === device.deviceHash) {
					targetDevice = device;
					break;
				}
			}

			if (!targetDevice) {
				return res.status(404).json({ error: "Device not found" });
			}

			// Update device metadata
			const updateData: Record<string, unknown> = {};
			if (deviceMetadata) {
				if (deviceMetadata.deviceName !== undefined)
					updateData.deviceName = deviceMetadata.deviceName;
				if (deviceMetadata.deviceType !== undefined)
					updateData.deviceType = deviceMetadata.deviceType;
				if (deviceMetadata.deviceModel !== undefined)
					updateData.deviceModel = deviceMetadata.deviceModel;
				if (deviceMetadata.operatingSystem !== undefined)
					updateData.operatingSystem = deviceMetadata.operatingSystem;
				if (deviceMetadata.architecture !== undefined)
					updateData.architecture = deviceMetadata.architecture;
				if (deviceMetadata.screenResolution !== undefined)
					updateData.screenResolution = deviceMetadata.screenResolution;
				if (deviceMetadata.totalMemory !== undefined)
					updateData.totalMemory = deviceMetadata.totalMemory;
				if (deviceMetadata.userNickname !== undefined)
					updateData.userNickname = deviceMetadata.userNickname;
				if (deviceMetadata.location !== undefined)
					updateData.location = deviceMetadata.location;
				if (deviceMetadata.notes !== undefined)
					updateData.notes = deviceMetadata.notes;
			}

			const updatedDevice = await prisma.device.update({
				where: { id: targetDevice.id },
				data: updateData,
			});

			const responseData = {
				message: "Device metadata updated successfully",
				device: {
					id: updatedDevice.id,
					deviceName: updatedDevice.deviceName,
					deviceType: updatedDevice.deviceType,
					deviceModel: updatedDevice.deviceModel,
					operatingSystem: updatedDevice.operatingSystem,
					architecture: updatedDevice.architecture,
					screenResolution: updatedDevice.screenResolution,
					totalMemory: updatedDevice.totalMemory,
					userNickname: updatedDevice.userNickname,
					location: updatedDevice.location,
					notes: updatedDevice.notes,
					lastSeen: updatedDevice.lastSeen,
					appVersion: updatedDevice.appVersion,
				},
			};

			// Log the response for debugging
			logApiResponse(
				EndpointPrefix.LICENSE_UPDATE_DEVICE,
				200,
				responseData,
				context,
				`Device metadata updated for device: ${targetDevice.id}`,
			);

			res.json(responseData);
		} catch (error) {
			console.error("Error updating device metadata:", error);

			if (error instanceof z.ZodError) {
				return res
					.status(400)
					.json({ error: "Invalid input data", details: error.message });
			}

			res.status(500).json({ error: "Internal server error" });
		}
	},
);

/**
 * @route GET /api/licenses/trial/status/:licenseKey
 * @description Check trial license status and remaining days
 * @access Public
 * @param {string} licenseKey - License key to check
 */
router.get(
	"/trial/status/:licenseKey",
	requestLoggingMiddleware(EndpointPrefix.LICENSE_STATUS),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			const { licenseKey } = req.params;

			Logger.info(
				EndpointPrefix.LICENSE_STATUS,
				`Checking trial status for license: ${licenseKey.substring(0, 4)}...`,
				context,
			);

			const result = await getTrialStatus(licenseKey);

			if (!result.success) {
				return sendErrorResponse(
					res,
					result.error || "Failed to check trial status",
					404,
					ErrorCode.LICENSE_NOT_FOUND,
				);
			}

			res.status(200).json({
				isTrialLicense: result.isTrialLicense,
				isExpired: result.isExpired,
				daysRemaining: result.daysRemaining,
				expiresAt: result.expiresAt,
			});
		} catch (error) {
			Logger.error(
				EndpointPrefix.LICENSE_STATUS,
				`Failed to check trial status: ${error}`,
				{ body: { error: String(error) } },
			);

			sendErrorResponse(
				res,
				"Failed to check trial status",
				500,
				ErrorCode.INTERNAL_ERROR,
			);
		}
	},
);

/**
 * @route POST /api/licenses/trial/convert
 * @description Convert trial license to paid license
 * @access Public
 * @param {string} licenseKey - Trial license key to convert
 * @param {string} newLicenseType - New license type (standard/extended)
 * @param {string} stripePaymentIntentId - Stripe payment intent ID
 * @param {number} additionalDevices - Additional devices to add
 */
router.post(
	"/trial/convert",
	createLicenseLimit, // Use same rate limit as license creation
	requestLoggingMiddleware(EndpointPrefix.LICENSE_UPGRADE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			const {
				licenseKey,
				newLicenseType,
				stripePaymentIntentId,
				additionalDevices = 0,
			} = req.body;

			// Validate input
			if (!licenseKey || !newLicenseType || !stripePaymentIntentId) {
				return sendErrorResponse(
					res,
					"Missing required fields",
					400,
					ErrorCode.VALIDATION_ERROR,
					"licenseKey, newLicenseType, and stripePaymentIntentId are required",
				);
			}

			if (!["standard", "extended"].includes(newLicenseType)) {
				return sendErrorResponse(
					res,
					"Invalid license type",
					400,
					ErrorCode.VALIDATION_ERROR,
					"newLicenseType must be 'standard' or 'extended'",
				);
			}

			Logger.info(
				EndpointPrefix.LICENSE_UPGRADE,
				`Converting trial license: ${licenseKey.substring(0, 4)}... to ${newLicenseType}`,
				{
					...context,
					body: {
						newLicenseType,
						additionalDevices,
					},
				},
			);

			const result = await convertTrialToPaid(
				licenseKey,
				newLicenseType,
				stripePaymentIntentId,
				additionalDevices,
			);

			if (!result.success) {
				const statusCode =
					result.errorCode === "LICENSE_NOT_FOUND"
						? 404
						: result.errorCode === "NOT_TRIAL_LICENSE"
							? 400
							: 500;

				return sendErrorResponse(
					res,
					result.error || "Failed to convert trial license",
					statusCode,
					result.errorCode as ErrorCode,
				);
			}

			// Log successful conversion
			await prisma.auditLog.create({
				data: {
					action: "LICENSE_CREATED",
					licenseKey,
					details: {
						action: "TRIAL_CONVERTED_VIA_API",
						newLicenseType,
						additionalDevices,
						stripePaymentIntentId,
					},
				},
			});

			res.status(200).json({
				message: "Trial license converted successfully",
				license: result.updatedLicense
					? {
							licenseKey: result.updatedLicense.licenseKey,
							licenseType: result.updatedLicense.licenseType,
							maxDevices: result.updatedLicense.maxDevices,
							expiresAt: result.updatedLicense.expiresAt,
						}
					: null,
			});
		} catch (error) {
			Logger.error(
				EndpointPrefix.LICENSE_UPGRADE,
				`Failed to convert trial license: ${error}`,
				{ body: { error: String(error) } },
			);

			sendErrorResponse(
				res,
				"Failed to convert trial license",
				500,
				ErrorCode.INTERNAL_ERROR,
			);
		}
	},
);

/**
 * @route GET /api/licenses/trial/info/:email
 * @description Get trial license information for an email
 * @access Public
 * @param {string} email - Email address to check for trial licenses
 */
router.get(
	"/trial/info/:email",
	requestLoggingMiddleware(EndpointPrefix.LICENSE_STATUS),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			const { email } = req.params;

			Logger.info(
				EndpointPrefix.LICENSE_STATUS,
				`Getting trial info for email: ${email}`,
				context,
			);

			const result = await getTrialLicenseInfo(email);

			res.status(200).json({
				hasTrialLicense: result.hasTrialLicense,
				trialLicense: result.trialLicense
					? {
							licenseKey: result.trialLicense.licenseKey,
							licenseType: result.trialLicense.licenseType,
							maxDevices: result.trialLicense.maxDevices,
							createdAt: result.trialLicense.createdAt,
							expiresAt: result.trialLicense.expiresAt,
						}
					: undefined,
				trialStatus: result.trialStatus,
			});
		} catch (error) {
			Logger.error(
				EndpointPrefix.LICENSE_STATUS,
				`Failed to get trial info: ${error}`,
				{ body: { error: String(error) } },
			);

			sendErrorResponse(
				res,
				"Failed to get trial information",
				500,
				ErrorCode.INTERNAL_ERROR,
			);
		}
	},
);

/**
 * @route GET /api/licenses/trial/registration-allowed
 * @description Check if new trial registrations are currently allowed
 * @access Public
 */
router.get(
	"/trial/registration-allowed",
	requestLoggingMiddleware(EndpointPrefix.GENERAL),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);

			Logger.info(
				EndpointPrefix.GENERAL,
				"Checking if trial registration is allowed",
				context,
			);

			const isAllowed = isTrialRegistrationAllowed();

			res.status(200).json({
				trialRegistrationAllowed: isAllowed,
				message: isAllowed
					? "Trial registrations are currently available"
					: "Trial registrations are no longer available. Please purchase a license directly.",
			});
		} catch (error) {
			Logger.error(
				EndpointPrefix.GENERAL,
				`Failed to check trial registration status: ${error}`,
				{ body: { error: String(error) } },
			);

			sendErrorResponse(
				res,
				"Failed to check trial registration status",
				500,
				ErrorCode.INTERNAL_ERROR,
			);
		}
	},
);

export default router;
