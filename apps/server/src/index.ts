import "dotenv/config";
import { to<PERSON><PERSON><PERSON><PERSON><PERSON> } from "better-auth/node";
import cors from "cors";
import express from "express";
import helmet from "helmet";
import { validateEnv } from "@/config/env";
import { generalApiLimit } from "@/middleware/rate-limit";
import licenseRouter from "@/routes/license";
import paymentRouter from "@/routes/payment";
import refundRouter from "@/routes/refund";
import userRouter from "@/routes/user";
import webhookRouter from "@/routes/webhook";
import { errorHandler } from "@/utils/errors";
import { auth } from "./lib/auth";

// Validate environment variables at startup
const env = validateEnv();

const app = express();

// Security middleware
app.use(
	helmet({
		contentSecurityPolicy: {
			directives: {
				defaultSrc: ["'self'"],
				scriptSrc: ["'self'", "js.stripe.com"],
				frameSrc: ["js.stripe.com"],
			},
		},
	}),
);

app.use(
	cors({
		origin: env.CORS_ORIGIN || "",
		methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
		allowedHeaders: ["Content-Type", "Authorization", "Cookie"],
		credentials: true,
		optionsSuccessStatus: 200, // Some legacy browsers choke on 204
	}),
);

// Authentication routes
app.all("/api/auth{/*path}", toNodeHandler(auth));

// Webhook routes MUST be registered before JSON body parser to preserve raw body
app.use("/api/stripe", webhookRouter);

app.use(express.json({ limit: "10mb" }));

// General rate limiting for all API routes
app.use("/api", generalApiLimit);

app.get("/", (_req, res) => {
	res.status(200).send("OK");
});

// API routes
app.use("/api/licenses", licenseRouter);
app.use("/api/payments", paymentRouter);
app.use("/api/refunds", refundRouter);
app.use("/api/users", userRouter);

// Error handling middleware
app.use(errorHandler);

app.use((_req, res) => {
	res.status(404).json({ error: "Endpoint not found" });
});

const port = env.PORT || 3000;
app.listen(port, () => {
	console.log(`Server is running on port ${port}`);
});
