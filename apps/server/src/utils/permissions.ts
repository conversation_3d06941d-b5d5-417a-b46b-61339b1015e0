/**
 * Permission utilities for RBAC system
 * Provides helper functions for checking user permissions and role hierarchies
 */

import type { User } from "prisma/generated/client";
import {
	ROLE_PERMISSIONS,
	USER_ROLE_HIERARCHY,
	type UserRole,
} from "@/middleware/auth";

/**
 * Check if a user has a specific permission
 */
export function userHasPermission(
	user: User,
	permission: keyof typeof ROLE_PERMISSIONS.SUPER_ADMIN,
): boolean {
	const userRole = user.role as UserRole;
	return ROLE_PERMISSIONS[userRole][permission];
}

/**
 * Check if a user can manage another user based on role hierarchy
 */
export function canUserManageUser(manager: User, target: User): boolean {
	const managerRole = manager.role as UserRole;
	const targetRole = target.role as UserRole;

	const managerIndex = USER_ROLE_HIERARCHY.indexOf(managerRole);
	const targetIndex = USER_ROLE_HIERARCHY.indexOf(targetRole);

	// Can only manage users with lower roles
	return managerIndex > targetIndex;
}

/**
 * Check if a user can assign a specific role
 */
export function canUserAssignRole(user: User, targetRole: UserRole): boolean {
	const userRole = user.role as UserRole;
	const userIndex = USER_ROLE_HIERARCHY.indexOf(userRole);
	const targetIndex = USER_ROLE_HIERARCHY.indexOf(targetRole);

	// Can only assign roles lower than or equal to their own (except SUPER_ADMIN can assign any role)
	if (userRole === "SUPER_ADMIN") {
		return true;
	}

	// ADMIN can assign any role except SUPER_ADMIN
	if (userRole === "ADMIN" && targetRole !== "SUPER_ADMIN") {
		return true;
	}

	// Others can only assign roles lower than their own
	return userIndex > targetIndex;
}

/**
 * Get the maximum role a user can assign to others
 */
export function getMaxAssignableRole(user: User): UserRole {
	const userRole = user.role as UserRole;

	if (userRole === "SUPER_ADMIN") {
		return "SUPER_ADMIN";
	}

	if (userRole === "ADMIN") {
		return "ADMIN";
	}

	const userIndex = USER_ROLE_HIERARCHY.indexOf(userRole as UserRole);
	if (userIndex > 0) {
		return USER_ROLE_HIERARCHY[userIndex - 1];
	}

	return "VIEWER";
}

/**
 * Get all roles that a user can assign
 */
export function getAssignableRoles(user: User): UserRole[] {
	const maxRole = getMaxAssignableRole(user);
	const maxIndex = USER_ROLE_HIERARCHY.indexOf(maxRole);

	return USER_ROLE_HIERARCHY.slice(0, maxIndex + 1);
}

/**
 * Check if a user can view another user's information
 */
export function canUserViewUser(viewer: User, target: User): boolean {
	// Users can always view their own information
	if (viewer.id === target.id) {
		return true;
	}

	// Check if user has permission to view users
	if (!userHasPermission(viewer, "canViewUsers")) {
		return false;
	}

	// If user can manage the target user, they can view them
	return canUserManageUser(viewer, target);
}

/**
 * Filter users based on what the current user can see
 */
export function filterViewableUsers(currentUser: User, users: User[]): User[] {
	return users.filter((user) => canUserViewUser(currentUser, user));
}

/**
 * Check if a user can invite users with a specific role
 */
export function canUserInviteWithRole(
	user: User,
	inviteRole: UserRole,
): boolean {
	// Must have invite permission
	if (!userHasPermission(user, "canInviteUsers")) {
		return false;
	}

	// Must be able to assign the role
	return canUserAssignRole(user, inviteRole);
}

/**
 * Get user permissions object for frontend
 */
export function getUserPermissionsObject(user: User) {
	const userRole = user.role as UserRole;
	return {
		...ROLE_PERMISSIONS[userRole],
		assignableRoles: getAssignableRoles(user),
		maxAssignableRole: getMaxAssignableRole(user),
	};
}

/**
 * Check if a role is higher than another role
 */
export function isRoleHigher(role1: UserRole, role2: UserRole): boolean {
	const index1 = USER_ROLE_HIERARCHY.indexOf(role1);
	const index2 = USER_ROLE_HIERARCHY.indexOf(role2);
	return index1 > index2;
}

/**
 * Check if a role is lower than another role
 */
export function isRoleLower(role1: UserRole, role2: UserRole): boolean {
	const index1 = USER_ROLE_HIERARCHY.indexOf(role1);
	const index2 = USER_ROLE_HIERARCHY.indexOf(role2);
	return index1 < index2;
}

/**
 * Get the role hierarchy level (0 = lowest, 4 = highest)
 */
export function getRoleLevel(role: UserRole): number {
	return USER_ROLE_HIERARCHY.indexOf(role);
}

/**
 * Validate if a role transition is allowed
 */
export function isRoleTransitionAllowed(
	currentUser: User,
	targetUser: User,
	newRole: UserRole,
): { allowed: boolean; reason?: string } {
	// Can't change your own role
	if (currentUser.id === targetUser.id) {
		return { allowed: false, reason: "Cannot change your own role" };
	}

	// Must be able to manage the target user
	if (!canUserManageUser(currentUser, targetUser)) {
		return {
			allowed: false,
			reason: "Insufficient permissions to manage this user",
		};
	}

	// Must be able to assign the new role
	if (!canUserAssignRole(currentUser, newRole)) {
		return { allowed: false, reason: `Cannot assign role: ${newRole}` };
	}

	return { allowed: true };
}

/**
 * Free Trial User Management Extensions
 * These functions help integrate trial users with the RBAC system
 */

/**
 * Check if a user has trial-related permissions
 * This is useful for showing trial upgrade options in the UI
 */
export function canUserManageTrials(user: User): boolean {
	// Only admins and super admins can manage trial-related features
	const userRole = user.role as UserRole;
	return userRole === "ADMIN" || userRole === "SUPER_ADMIN";
}

/**
 * Get trial-specific permissions for a user
 * This extends the regular permissions with trial-related capabilities
 */
export function getTrialPermissions(user: User) {
	const basePermissions = getUserPermissionsObject(user);

	return {
		...basePermissions,
		canViewTrialInfo: true, // All users can view their own trial info
		canUpgradeTrials: true, // All users can upgrade their own trials
		canManageTrials: canUserManageTrials(user), // Only admins can manage all trials
		canViewTrialAnalytics: canUserManageTrials(user), // Only admins can view trial analytics
	};
}

/**
 * Check if a user should see trial upgrade prompts
 * This can be used to customize the UI based on user status
 */
export async function shouldShowTrialUpgradePrompts(
	user: User,
): Promise<boolean> {
	try {
		// Import here to avoid circular dependencies
		const { getTrialLicenseInfo } = await import("@/services/license");

		// Check if user has any trial licenses
		const trialInfo = await getTrialLicenseInfo(user.email);

		// Show upgrade prompts if user has trial licenses
		return trialInfo.hasTrialLicense;
	} catch (_error) {
		// If we can't check trial status, don't show prompts
		return false;
	}
}

/**
 * Get user context with trial information
 * This provides a complete picture of the user including trial status
 */
export async function getUserContextWithTrials(user: User): Promise<{
	user: User;
	permissions: ReturnType<typeof getTrialPermissions>;
	trialInfo?: {
		hasTrialLicense: boolean;
		shouldShowUpgradePrompts: boolean;
		trialStatus?: {
			success: boolean;
			isTrialLicense: boolean;
			isExpired?: boolean;
			daysRemaining?: number;
			expiresAt?: Date;
		};
	};
}> {
	try {
		const permissions = getTrialPermissions(user);
		const shouldShowUpgradePrompts = await shouldShowTrialUpgradePrompts(user);

		let trialInfo:
			| {
					hasTrialLicense: boolean;
					shouldShowUpgradePrompts: boolean;
					trialStatus?: {
						success: boolean;
						isTrialLicense: boolean;
						isExpired?: boolean;
						daysRemaining?: number;
						expiresAt?: Date;
					};
			  }
			| undefined;
		if (shouldShowUpgradePrompts) {
			const { getTrialLicenseInfo } = await import("@/services/license");
			const licenseInfo = await getTrialLicenseInfo(user.email);

			trialInfo = {
				hasTrialLicense: licenseInfo.hasTrialLicense,
				shouldShowUpgradePrompts,
				trialStatus: licenseInfo.trialStatus,
			};
		}

		return {
			user,
			permissions,
			trialInfo,
		};
	} catch {
		// If there's an error, return basic user context
		return {
			user,
			permissions: getTrialPermissions(user),
		};
	}
}
