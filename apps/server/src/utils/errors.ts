import type { NextFunction, Request, Response } from "express";
import { z } from "zod";

/**
 * Prisma error interface for database operations
 */
export interface PrismaError extends Error {
	code?: string;
	meta?: {
		target?: string[];
		field_name?: string;
	};
}

/**
 * Stripe error interface for payment operations
 */
export interface StripeError extends Error {
	type?: string;
	code?: string;
	decline_code?: string;
	param?: string;
	payment_intent?: {
		id: string;
		status: string;
	};
}

/**
 * Generic error with optional code property
 */
interface ErrorWithCode extends Error {
	code?: string;
	statusCode?: number;
}

/**
 * Standard error response interface
 */
export interface ErrorResponse {
	error: string;
	message?: string;
	details?: Record<string, unknown>;
	code?: string;
	timestamp: string;
	path?: string;
}

/**
 * Error codes for different types of errors
 */
export enum ErrorCode {
	// Validation errors
	VALIDATION_ERROR = "VALIDATION_ERROR",
	INVALID_INPUT = "INVALID_INPUT",
	MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD",

	// Authentication/Authorization errors
	UNAUTHORIZED = "UNAUTHORIZED",
	FORBIDDEN = "FORBIDDEN",
	INVALID_TOKEN = "INVALID_TOKEN",
	TOKEN_EXPIRED = "TOKEN_EXPIRED",

	// Resource errors
	NOT_FOUND = "NOT_FOUND",
	ALREADY_EXISTS = "ALREADY_EXISTS",
	CONFLICT = "CONFLICT",

	// License-specific errors
	LICENSE_NOT_FOUND = "LICENSE_NOT_FOUND",
	LICENSE_EXPIRED = "LICENSE_EXPIRED",
	LICENSE_INVALID = "LICENSE_INVALID",
	MAX_DEVICES_REACHED = "MAX_DEVICES_REACHED",
	DEVICE_NOT_REGISTERED = "DEVICE_NOT_REGISTERED",

	// Payment errors
	PAYMENT_FAILED = "PAYMENT_FAILED",
	PAYMENT_INCOMPLETE = "PAYMENT_INCOMPLETE",
	INVALID_PAYMENT = "INVALID_PAYMENT",

	// Rate limiting
	RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED",

	// User management errors
	USER_NOT_FOUND = "USER_NOT_FOUND",
	USER_ALREADY_EXISTS = "USER_ALREADY_EXISTS",
	INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS",
	INVALID_ROLE_ASSIGNMENT = "INVALID_ROLE_ASSIGNMENT",
	INVALID_ROLE_CHANGE = "INVALID_ROLE_CHANGE",
	CANNOT_MODIFY_SELF = "CANNOT_MODIFY_SELF",
	ACCOUNT_DEACTIVATED = "ACCOUNT_DEACTIVATED",

	// Invitation errors
	INVITATION_NOT_FOUND = "INVITATION_NOT_FOUND",
	INVITATION_ALREADY_EXISTS = "INVITATION_ALREADY_EXISTS",
	INVITATION_EXPIRED = "INVITATION_EXPIRED",
	INVITATION_INVALID = "INVITATION_INVALID",

	// Server errors
	INTERNAL_ERROR = "INTERNAL_ERROR",
	DATABASE_ERROR = "DATABASE_ERROR",
	EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR",
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
	error: string,
	statusCode = 500,
	code?: ErrorCode,
	message?: string,
	details?: Record<string, unknown>,
	path?: string,
): { statusCode: number; response: ErrorResponse } {
	return {
		statusCode,
		response: {
			error,
			message,
			details,
			code,
			timestamp: new Date().toISOString(),
			path,
		},
	};
}

/**
 * Send a standardized error response
 */
export function sendErrorResponse(
	res: Response,
	error: string,
	statusCode = 500,
	code?: ErrorCode,
	message?: string,
	details?: Record<string, unknown>,
): void {
	const path = res.req?.originalUrl || res.req?.url;
	const { response } = createErrorResponse(
		error,
		statusCode,
		code,
		message,
		details,
		path,
	);

	// Log error for debugging (exclude sensitive information)
	if (statusCode >= 500) {
		console.error(`[${response.timestamp}] ${statusCode} ${error}`, {
			path,
			code,
			message,
			// Don't log sensitive details in production
			details: process.env.NODE_ENV === "development" ? details : undefined,
		});
	}

	res.status(statusCode).json(response);
}

/**
 * Handle Zod validation errors
 */
export function handleZodError(res: Response, error: z.ZodError): void {
	const validationErrors = error.issues.map((issue) => ({
		field: issue.path.join("."),
		message: issue.message,
		code: issue.code,
	}));

	sendErrorResponse(
		res,
		"Validation failed",
		400,
		ErrorCode.VALIDATION_ERROR,
		"The provided data is invalid",
		{ validationErrors },
	);
}

/**
 * Handle database errors
 */
export function handleDatabaseError(res: Response, error: PrismaError): void {
	console.error("Database error:", error);

	// Check for common Prisma errors
	if (error.code === "P2002") {
		// Unique constraint violation
		sendErrorResponse(
			res,
			"Resource already exists",
			409,
			ErrorCode.ALREADY_EXISTS,
			"A resource with this information already exists",
		);
		return;
	}

	if (error.code === "P2025") {
		// Record not found
		sendErrorResponse(
			res,
			"Resource not found",
			404,
			ErrorCode.NOT_FOUND,
			"The requested resource was not found",
		);
		return;
	}

	// Generic database error
	sendErrorResponse(
		res,
		"Database operation failed",
		500,
		ErrorCode.DATABASE_ERROR,
		"An error occurred while accessing the database",
	);
}

/**
 * Handle Stripe payment errors
 */
export function handleStripeError(res: Response, error: StripeError): void {
	console.error("Stripe error:", error);

	if (error.type === "StripeCardError") {
		sendErrorResponse(
			res,
			"Payment failed",
			400,
			ErrorCode.PAYMENT_FAILED,
			error.message || "Your card was declined",
		);
		return;
	}

	if (error.type === "StripeInvalidRequestError") {
		sendErrorResponse(
			res,
			"Invalid payment request",
			400,
			ErrorCode.INVALID_PAYMENT,
			"The payment request is invalid",
		);
		return;
	}

	// Generic Stripe error
	sendErrorResponse(
		res,
		"Payment processing error",
		500,
		ErrorCode.EXTERNAL_SERVICE_ERROR,
		"An error occurred while processing your payment",
	);
}

/**
 * License-specific error responses
 */
export const LicenseErrors = {
	notFound: (res: Response): void =>
		sendErrorResponse(
			res,
			"License not found",
			404,
			ErrorCode.LICENSE_NOT_FOUND,
			"The specified license key was not found",
		),

	expired: (res: Response): void =>
		sendErrorResponse(
			res,
			"License expired",
			403,
			ErrorCode.LICENSE_EXPIRED,
			"This license has expired and is no longer valid",
		),

	maxDevicesReached: (
		res: Response,
		maxDevices: number,
		currentDevices: number,
	): void =>
		sendErrorResponse(
			res,
			"Maximum devices reached",
			403,
			ErrorCode.MAX_DEVICES_REACHED,
			`This license supports a maximum of ${maxDevices} devices`,
			{ maxDevices, currentDevices },
		),

	alreadyExists: (res: Response, licenseType: string): void =>
		sendErrorResponse(
			res,
			"License already exists",
			409,
			ErrorCode.ALREADY_EXISTS,
			"An active license already exists for this email address",
			{ existingLicenseType: licenseType },
		),

	refunded: (res: Response, refundedAt: Date): void =>
		sendErrorResponse(
			res,
			"License refunded",
			403,
			ErrorCode.LICENSE_EXPIRED,
			"This license has been refunded and is no longer valid",
			{ refundedAt: refundedAt.toISOString() },
		),
} as const;

/**
 * Payment-specific error responses
 */
export const PaymentErrors = {
	incomplete: (res: Response, paymentStatus: string): void =>
		sendErrorResponse(
			res,
			"Payment not completed",
			400,
			ErrorCode.PAYMENT_INCOMPLETE,
			"The payment has not been completed successfully",
			{ paymentStatus },
		),

	failed: (res: Response, reason?: string): void =>
		sendErrorResponse(
			res,
			"Payment failed",
			400,
			ErrorCode.PAYMENT_FAILED,
			reason || "The payment could not be processed",
		),
} as const;

/**
 * User management error responses
 */
export const UserErrors = {
	notFound: (res: Response): void =>
		sendErrorResponse(
			res,
			"User not found",
			404,
			ErrorCode.USER_NOT_FOUND,
			"The specified user was not found",
		),

	alreadyExists: (res: Response, email: string): void =>
		sendErrorResponse(
			res,
			"User already exists",
			409,
			ErrorCode.USER_ALREADY_EXISTS,
			"A user with this email address already exists",
			{ email },
		),

	insufficientPermissions: (res: Response, action?: string): void =>
		sendErrorResponse(
			res,
			"Insufficient permissions",
			403,
			ErrorCode.INSUFFICIENT_PERMISSIONS,
			action
				? `You don't have permission to ${action}`
				: "You don't have permission to perform this action",
		),

	invalidRoleAssignment: (res: Response, role: string): void =>
		sendErrorResponse(
			res,
			"Invalid role assignment",
			403,
			ErrorCode.INVALID_ROLE_ASSIGNMENT,
			`You cannot assign the role: ${role}`,
			{ attemptedRole: role },
		),

	cannotModifySelf: (res: Response, action: string): void =>
		sendErrorResponse(
			res,
			"Cannot modify own account",
			400,
			ErrorCode.CANNOT_MODIFY_SELF,
			`You cannot ${action} your own account`,
		),

	accountDeactivated: (res: Response): void =>
		sendErrorResponse(
			res,
			"Account deactivated",
			403,
			ErrorCode.ACCOUNT_DEACTIVATED,
			"Your account has been deactivated. Please contact an administrator.",
		),

	invitationNotFound: (res: Response): void =>
		sendErrorResponse(
			res,
			"Invitation not found",
			404,
			ErrorCode.INVITATION_NOT_FOUND,
			"The specified invitation was not found or has expired",
		),

	invitationAlreadyExists: (res: Response, email: string): void =>
		sendErrorResponse(
			res,
			"Invitation already exists",
			409,
			ErrorCode.INVITATION_ALREADY_EXISTS,
			"An invitation has already been sent to this email address",
			{ email },
		),

	invitationExpired: (res: Response): void =>
		sendErrorResponse(
			res,
			"Invitation expired",
			400,
			ErrorCode.INVITATION_EXPIRED,
			"This invitation has expired. Please request a new invitation.",
		),
} as const;

/**
 * Generic error handler middleware
 */
export function errorHandler(
	error: ErrorWithCode | z.ZodError | PrismaError | StripeError | Error,
	_req: Request,
	res: Response,
	_next: NextFunction,
): void {
	// Handle Zod validation errors
	if (error instanceof z.ZodError) {
		handleZodError(res, error);
		return;
	}

	// Handle database errors
	if (
		"code" in error &&
		typeof error.code === "string" &&
		error.code.startsWith("P")
	) {
		handleDatabaseError(res, error as PrismaError);
		return;
	}

	// Handle Stripe errors
	if (
		"type" in error &&
		typeof error.type === "string" &&
		error.type.startsWith("Stripe")
	) {
		handleStripeError(res, error as StripeError);
		return;
	}

	// Generic error
	sendErrorResponse(
		res,
		"Internal server error",
		500,
		ErrorCode.INTERNAL_ERROR,
		"An unexpected error occurred",
	);
}
