
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `RefundRequest` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model RefundRequest
 * 
 */
export type RefundRequestModel = runtime.Types.Result.DefaultSelection<Prisma.$RefundRequestPayload>

export type AggregateRefundRequest = {
  _count: RefundRequestCountAggregateOutputType | null
  _avg: RefundRequestAvgAggregateOutputType | null
  _sum: RefundRequestSumAggregateOutputType | null
  _min: RefundRequestMinAggregateOutputType | null
  _max: RefundRequestMaxAggregateOutputType | null
}

export type RefundRequestAvgAggregateOutputType = {
  amount: number | null
}

export type RefundRequestSumAggregateOutputType = {
  amount: number | null
}

export type RefundRequestMinAggregateOutputType = {
  id: string | null
  licenseId: string | null
  requestedBy: string | null
  reason: string | null
  status: $Enums.RefundStatus | null
  amount: number | null
  adminNotes: string | null
  processedBy: string | null
  processedAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type RefundRequestMaxAggregateOutputType = {
  id: string | null
  licenseId: string | null
  requestedBy: string | null
  reason: string | null
  status: $Enums.RefundStatus | null
  amount: number | null
  adminNotes: string | null
  processedBy: string | null
  processedAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type RefundRequestCountAggregateOutputType = {
  id: number
  licenseId: number
  requestedBy: number
  reason: number
  status: number
  amount: number
  adminNotes: number
  processedBy: number
  processedAt: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type RefundRequestAvgAggregateInputType = {
  amount?: true
}

export type RefundRequestSumAggregateInputType = {
  amount?: true
}

export type RefundRequestMinAggregateInputType = {
  id?: true
  licenseId?: true
  requestedBy?: true
  reason?: true
  status?: true
  amount?: true
  adminNotes?: true
  processedBy?: true
  processedAt?: true
  createdAt?: true
  updatedAt?: true
}

export type RefundRequestMaxAggregateInputType = {
  id?: true
  licenseId?: true
  requestedBy?: true
  reason?: true
  status?: true
  amount?: true
  adminNotes?: true
  processedBy?: true
  processedAt?: true
  createdAt?: true
  updatedAt?: true
}

export type RefundRequestCountAggregateInputType = {
  id?: true
  licenseId?: true
  requestedBy?: true
  reason?: true
  status?: true
  amount?: true
  adminNotes?: true
  processedBy?: true
  processedAt?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type RefundRequestAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which RefundRequest to aggregate.
   */
  where?: Prisma.RefundRequestWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RefundRequests to fetch.
   */
  orderBy?: Prisma.RefundRequestOrderByWithRelationInput | Prisma.RefundRequestOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.RefundRequestWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RefundRequests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RefundRequests.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned RefundRequests
  **/
  _count?: true | RefundRequestCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: RefundRequestAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: RefundRequestSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: RefundRequestMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: RefundRequestMaxAggregateInputType
}

export type GetRefundRequestAggregateType<T extends RefundRequestAggregateArgs> = {
      [P in keyof T & keyof AggregateRefundRequest]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateRefundRequest[P]>
    : Prisma.GetScalarType<T[P], AggregateRefundRequest[P]>
}




export type RefundRequestGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RefundRequestWhereInput
  orderBy?: Prisma.RefundRequestOrderByWithAggregationInput | Prisma.RefundRequestOrderByWithAggregationInput[]
  by: Prisma.RefundRequestScalarFieldEnum[] | Prisma.RefundRequestScalarFieldEnum
  having?: Prisma.RefundRequestScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: RefundRequestCountAggregateInputType | true
  _avg?: RefundRequestAvgAggregateInputType
  _sum?: RefundRequestSumAggregateInputType
  _min?: RefundRequestMinAggregateInputType
  _max?: RefundRequestMaxAggregateInputType
}

export type RefundRequestGroupByOutputType = {
  id: string
  licenseId: string
  requestedBy: string
  reason: string
  status: $Enums.RefundStatus
  amount: number | null
  adminNotes: string | null
  processedBy: string | null
  processedAt: Date | null
  createdAt: Date
  updatedAt: Date
  _count: RefundRequestCountAggregateOutputType | null
  _avg: RefundRequestAvgAggregateOutputType | null
  _sum: RefundRequestSumAggregateOutputType | null
  _min: RefundRequestMinAggregateOutputType | null
  _max: RefundRequestMaxAggregateOutputType | null
}

type GetRefundRequestGroupByPayload<T extends RefundRequestGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<RefundRequestGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof RefundRequestGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], RefundRequestGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], RefundRequestGroupByOutputType[P]>
      }
    >
  >



export type RefundRequestWhereInput = {
  AND?: Prisma.RefundRequestWhereInput | Prisma.RefundRequestWhereInput[]
  OR?: Prisma.RefundRequestWhereInput[]
  NOT?: Prisma.RefundRequestWhereInput | Prisma.RefundRequestWhereInput[]
  id?: Prisma.StringFilter<"RefundRequest"> | string
  licenseId?: Prisma.StringFilter<"RefundRequest"> | string
  requestedBy?: Prisma.StringFilter<"RefundRequest"> | string
  reason?: Prisma.StringFilter<"RefundRequest"> | string
  status?: Prisma.EnumRefundStatusFilter<"RefundRequest"> | $Enums.RefundStatus
  amount?: Prisma.IntNullableFilter<"RefundRequest"> | number | null
  adminNotes?: Prisma.StringNullableFilter<"RefundRequest"> | string | null
  processedBy?: Prisma.StringNullableFilter<"RefundRequest"> | string | null
  processedAt?: Prisma.DateTimeNullableFilter<"RefundRequest"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"RefundRequest"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"RefundRequest"> | Date | string
  license?: Prisma.XOR<Prisma.LicenseScalarRelationFilter, Prisma.LicenseWhereInput>
}

export type RefundRequestOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  licenseId?: Prisma.SortOrder
  requestedBy?: Prisma.SortOrder
  reason?: Prisma.SortOrder
  status?: Prisma.SortOrder
  amount?: Prisma.SortOrderInput | Prisma.SortOrder
  adminNotes?: Prisma.SortOrderInput | Prisma.SortOrder
  processedBy?: Prisma.SortOrderInput | Prisma.SortOrder
  processedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  license?: Prisma.LicenseOrderByWithRelationInput
}

export type RefundRequestWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  licenseId?: string
  AND?: Prisma.RefundRequestWhereInput | Prisma.RefundRequestWhereInput[]
  OR?: Prisma.RefundRequestWhereInput[]
  NOT?: Prisma.RefundRequestWhereInput | Prisma.RefundRequestWhereInput[]
  requestedBy?: Prisma.StringFilter<"RefundRequest"> | string
  reason?: Prisma.StringFilter<"RefundRequest"> | string
  status?: Prisma.EnumRefundStatusFilter<"RefundRequest"> | $Enums.RefundStatus
  amount?: Prisma.IntNullableFilter<"RefundRequest"> | number | null
  adminNotes?: Prisma.StringNullableFilter<"RefundRequest"> | string | null
  processedBy?: Prisma.StringNullableFilter<"RefundRequest"> | string | null
  processedAt?: Prisma.DateTimeNullableFilter<"RefundRequest"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"RefundRequest"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"RefundRequest"> | Date | string
  license?: Prisma.XOR<Prisma.LicenseScalarRelationFilter, Prisma.LicenseWhereInput>
}, "id" | "licenseId">

export type RefundRequestOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  licenseId?: Prisma.SortOrder
  requestedBy?: Prisma.SortOrder
  reason?: Prisma.SortOrder
  status?: Prisma.SortOrder
  amount?: Prisma.SortOrderInput | Prisma.SortOrder
  adminNotes?: Prisma.SortOrderInput | Prisma.SortOrder
  processedBy?: Prisma.SortOrderInput | Prisma.SortOrder
  processedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.RefundRequestCountOrderByAggregateInput
  _avg?: Prisma.RefundRequestAvgOrderByAggregateInput
  _max?: Prisma.RefundRequestMaxOrderByAggregateInput
  _min?: Prisma.RefundRequestMinOrderByAggregateInput
  _sum?: Prisma.RefundRequestSumOrderByAggregateInput
}

export type RefundRequestScalarWhereWithAggregatesInput = {
  AND?: Prisma.RefundRequestScalarWhereWithAggregatesInput | Prisma.RefundRequestScalarWhereWithAggregatesInput[]
  OR?: Prisma.RefundRequestScalarWhereWithAggregatesInput[]
  NOT?: Prisma.RefundRequestScalarWhereWithAggregatesInput | Prisma.RefundRequestScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"RefundRequest"> | string
  licenseId?: Prisma.StringWithAggregatesFilter<"RefundRequest"> | string
  requestedBy?: Prisma.StringWithAggregatesFilter<"RefundRequest"> | string
  reason?: Prisma.StringWithAggregatesFilter<"RefundRequest"> | string
  status?: Prisma.EnumRefundStatusWithAggregatesFilter<"RefundRequest"> | $Enums.RefundStatus
  amount?: Prisma.IntNullableWithAggregatesFilter<"RefundRequest"> | number | null
  adminNotes?: Prisma.StringNullableWithAggregatesFilter<"RefundRequest"> | string | null
  processedBy?: Prisma.StringNullableWithAggregatesFilter<"RefundRequest"> | string | null
  processedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"RefundRequest"> | Date | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"RefundRequest"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"RefundRequest"> | Date | string
}

export type RefundRequestCreateInput = {
  id?: string
  requestedBy: string
  reason: string
  status?: $Enums.RefundStatus
  amount?: number | null
  adminNotes?: string | null
  processedBy?: string | null
  processedAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  license: Prisma.LicenseCreateNestedOneWithoutRefundRequestInput
}

export type RefundRequestUncheckedCreateInput = {
  id?: string
  licenseId: string
  requestedBy: string
  reason: string
  status?: $Enums.RefundStatus
  amount?: number | null
  adminNotes?: string | null
  processedBy?: string | null
  processedAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RefundRequestUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  requestedBy?: Prisma.StringFieldUpdateOperationsInput | string
  reason?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRefundStatusFieldUpdateOperationsInput | $Enums.RefundStatus
  amount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  adminNotes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  processedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  license?: Prisma.LicenseUpdateOneRequiredWithoutRefundRequestNestedInput
}

export type RefundRequestUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseId?: Prisma.StringFieldUpdateOperationsInput | string
  requestedBy?: Prisma.StringFieldUpdateOperationsInput | string
  reason?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRefundStatusFieldUpdateOperationsInput | $Enums.RefundStatus
  amount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  adminNotes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  processedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RefundRequestCreateManyInput = {
  id?: string
  licenseId: string
  requestedBy: string
  reason: string
  status?: $Enums.RefundStatus
  amount?: number | null
  adminNotes?: string | null
  processedBy?: string | null
  processedAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RefundRequestUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  requestedBy?: Prisma.StringFieldUpdateOperationsInput | string
  reason?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRefundStatusFieldUpdateOperationsInput | $Enums.RefundStatus
  amount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  adminNotes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  processedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RefundRequestUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseId?: Prisma.StringFieldUpdateOperationsInput | string
  requestedBy?: Prisma.StringFieldUpdateOperationsInput | string
  reason?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRefundStatusFieldUpdateOperationsInput | $Enums.RefundStatus
  amount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  adminNotes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  processedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RefundRequestNullableScalarRelationFilter = {
  is?: Prisma.RefundRequestWhereInput | null
  isNot?: Prisma.RefundRequestWhereInput | null
}

export type RefundRequestCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  licenseId?: Prisma.SortOrder
  requestedBy?: Prisma.SortOrder
  reason?: Prisma.SortOrder
  status?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  adminNotes?: Prisma.SortOrder
  processedBy?: Prisma.SortOrder
  processedAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type RefundRequestAvgOrderByAggregateInput = {
  amount?: Prisma.SortOrder
}

export type RefundRequestMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  licenseId?: Prisma.SortOrder
  requestedBy?: Prisma.SortOrder
  reason?: Prisma.SortOrder
  status?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  adminNotes?: Prisma.SortOrder
  processedBy?: Prisma.SortOrder
  processedAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type RefundRequestMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  licenseId?: Prisma.SortOrder
  requestedBy?: Prisma.SortOrder
  reason?: Prisma.SortOrder
  status?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  adminNotes?: Prisma.SortOrder
  processedBy?: Prisma.SortOrder
  processedAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type RefundRequestSumOrderByAggregateInput = {
  amount?: Prisma.SortOrder
}

export type RefundRequestCreateNestedOneWithoutLicenseInput = {
  create?: Prisma.XOR<Prisma.RefundRequestCreateWithoutLicenseInput, Prisma.RefundRequestUncheckedCreateWithoutLicenseInput>
  connectOrCreate?: Prisma.RefundRequestCreateOrConnectWithoutLicenseInput
  connect?: Prisma.RefundRequestWhereUniqueInput
}

export type RefundRequestUncheckedCreateNestedOneWithoutLicenseInput = {
  create?: Prisma.XOR<Prisma.RefundRequestCreateWithoutLicenseInput, Prisma.RefundRequestUncheckedCreateWithoutLicenseInput>
  connectOrCreate?: Prisma.RefundRequestCreateOrConnectWithoutLicenseInput
  connect?: Prisma.RefundRequestWhereUniqueInput
}

export type RefundRequestUpdateOneWithoutLicenseNestedInput = {
  create?: Prisma.XOR<Prisma.RefundRequestCreateWithoutLicenseInput, Prisma.RefundRequestUncheckedCreateWithoutLicenseInput>
  connectOrCreate?: Prisma.RefundRequestCreateOrConnectWithoutLicenseInput
  upsert?: Prisma.RefundRequestUpsertWithoutLicenseInput
  disconnect?: Prisma.RefundRequestWhereInput | boolean
  delete?: Prisma.RefundRequestWhereInput | boolean
  connect?: Prisma.RefundRequestWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.RefundRequestUpdateToOneWithWhereWithoutLicenseInput, Prisma.RefundRequestUpdateWithoutLicenseInput>, Prisma.RefundRequestUncheckedUpdateWithoutLicenseInput>
}

export type RefundRequestUncheckedUpdateOneWithoutLicenseNestedInput = {
  create?: Prisma.XOR<Prisma.RefundRequestCreateWithoutLicenseInput, Prisma.RefundRequestUncheckedCreateWithoutLicenseInput>
  connectOrCreate?: Prisma.RefundRequestCreateOrConnectWithoutLicenseInput
  upsert?: Prisma.RefundRequestUpsertWithoutLicenseInput
  disconnect?: Prisma.RefundRequestWhereInput | boolean
  delete?: Prisma.RefundRequestWhereInput | boolean
  connect?: Prisma.RefundRequestWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.RefundRequestUpdateToOneWithWhereWithoutLicenseInput, Prisma.RefundRequestUpdateWithoutLicenseInput>, Prisma.RefundRequestUncheckedUpdateWithoutLicenseInput>
}

export type EnumRefundStatusFieldUpdateOperationsInput = {
  set?: $Enums.RefundStatus
}

export type RefundRequestCreateWithoutLicenseInput = {
  id?: string
  requestedBy: string
  reason: string
  status?: $Enums.RefundStatus
  amount?: number | null
  adminNotes?: string | null
  processedBy?: string | null
  processedAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RefundRequestUncheckedCreateWithoutLicenseInput = {
  id?: string
  requestedBy: string
  reason: string
  status?: $Enums.RefundStatus
  amount?: number | null
  adminNotes?: string | null
  processedBy?: string | null
  processedAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RefundRequestCreateOrConnectWithoutLicenseInput = {
  where: Prisma.RefundRequestWhereUniqueInput
  create: Prisma.XOR<Prisma.RefundRequestCreateWithoutLicenseInput, Prisma.RefundRequestUncheckedCreateWithoutLicenseInput>
}

export type RefundRequestUpsertWithoutLicenseInput = {
  update: Prisma.XOR<Prisma.RefundRequestUpdateWithoutLicenseInput, Prisma.RefundRequestUncheckedUpdateWithoutLicenseInput>
  create: Prisma.XOR<Prisma.RefundRequestCreateWithoutLicenseInput, Prisma.RefundRequestUncheckedCreateWithoutLicenseInput>
  where?: Prisma.RefundRequestWhereInput
}

export type RefundRequestUpdateToOneWithWhereWithoutLicenseInput = {
  where?: Prisma.RefundRequestWhereInput
  data: Prisma.XOR<Prisma.RefundRequestUpdateWithoutLicenseInput, Prisma.RefundRequestUncheckedUpdateWithoutLicenseInput>
}

export type RefundRequestUpdateWithoutLicenseInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  requestedBy?: Prisma.StringFieldUpdateOperationsInput | string
  reason?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRefundStatusFieldUpdateOperationsInput | $Enums.RefundStatus
  amount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  adminNotes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  processedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RefundRequestUncheckedUpdateWithoutLicenseInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  requestedBy?: Prisma.StringFieldUpdateOperationsInput | string
  reason?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRefundStatusFieldUpdateOperationsInput | $Enums.RefundStatus
  amount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  adminNotes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  processedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type RefundRequestSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  licenseId?: boolean
  requestedBy?: boolean
  reason?: boolean
  status?: boolean
  amount?: boolean
  adminNotes?: boolean
  processedBy?: boolean
  processedAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>
}, ExtArgs["result"]["refundRequest"]>

export type RefundRequestSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  licenseId?: boolean
  requestedBy?: boolean
  reason?: boolean
  status?: boolean
  amount?: boolean
  adminNotes?: boolean
  processedBy?: boolean
  processedAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>
}, ExtArgs["result"]["refundRequest"]>

export type RefundRequestSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  licenseId?: boolean
  requestedBy?: boolean
  reason?: boolean
  status?: boolean
  amount?: boolean
  adminNotes?: boolean
  processedBy?: boolean
  processedAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>
}, ExtArgs["result"]["refundRequest"]>

export type RefundRequestSelectScalar = {
  id?: boolean
  licenseId?: boolean
  requestedBy?: boolean
  reason?: boolean
  status?: boolean
  amount?: boolean
  adminNotes?: boolean
  processedBy?: boolean
  processedAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type RefundRequestOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "licenseId" | "requestedBy" | "reason" | "status" | "amount" | "adminNotes" | "processedBy" | "processedAt" | "createdAt" | "updatedAt", ExtArgs["result"]["refundRequest"]>
export type RefundRequestInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>
}
export type RefundRequestIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>
}
export type RefundRequestIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>
}

export type $RefundRequestPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "RefundRequest"
  objects: {
    license: Prisma.$LicensePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    licenseId: string
    requestedBy: string
    reason: string
    status: $Enums.RefundStatus
    amount: number | null
    adminNotes: string | null
    processedBy: string | null
    processedAt: Date | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["refundRequest"]>
  composites: {}
}

export type RefundRequestGetPayload<S extends boolean | null | undefined | RefundRequestDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload, S>

export type RefundRequestCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<RefundRequestFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: RefundRequestCountAggregateInputType | true
  }

export interface RefundRequestDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['RefundRequest'], meta: { name: 'RefundRequest' } }
  /**
   * Find zero or one RefundRequest that matches the filter.
   * @param {RefundRequestFindUniqueArgs} args - Arguments to find a RefundRequest
   * @example
   * // Get one RefundRequest
   * const refundRequest = await prisma.refundRequest.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends RefundRequestFindUniqueArgs>(args: Prisma.SelectSubset<T, RefundRequestFindUniqueArgs<ExtArgs>>): Prisma.Prisma__RefundRequestClient<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one RefundRequest that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {RefundRequestFindUniqueOrThrowArgs} args - Arguments to find a RefundRequest
   * @example
   * // Get one RefundRequest
   * const refundRequest = await prisma.refundRequest.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends RefundRequestFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, RefundRequestFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__RefundRequestClient<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first RefundRequest that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RefundRequestFindFirstArgs} args - Arguments to find a RefundRequest
   * @example
   * // Get one RefundRequest
   * const refundRequest = await prisma.refundRequest.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends RefundRequestFindFirstArgs>(args?: Prisma.SelectSubset<T, RefundRequestFindFirstArgs<ExtArgs>>): Prisma.Prisma__RefundRequestClient<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first RefundRequest that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RefundRequestFindFirstOrThrowArgs} args - Arguments to find a RefundRequest
   * @example
   * // Get one RefundRequest
   * const refundRequest = await prisma.refundRequest.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends RefundRequestFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, RefundRequestFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__RefundRequestClient<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more RefundRequests that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RefundRequestFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all RefundRequests
   * const refundRequests = await prisma.refundRequest.findMany()
   * 
   * // Get first 10 RefundRequests
   * const refundRequests = await prisma.refundRequest.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const refundRequestWithIdOnly = await prisma.refundRequest.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends RefundRequestFindManyArgs>(args?: Prisma.SelectSubset<T, RefundRequestFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a RefundRequest.
   * @param {RefundRequestCreateArgs} args - Arguments to create a RefundRequest.
   * @example
   * // Create one RefundRequest
   * const RefundRequest = await prisma.refundRequest.create({
   *   data: {
   *     // ... data to create a RefundRequest
   *   }
   * })
   * 
   */
  create<T extends RefundRequestCreateArgs>(args: Prisma.SelectSubset<T, RefundRequestCreateArgs<ExtArgs>>): Prisma.Prisma__RefundRequestClient<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many RefundRequests.
   * @param {RefundRequestCreateManyArgs} args - Arguments to create many RefundRequests.
   * @example
   * // Create many RefundRequests
   * const refundRequest = await prisma.refundRequest.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends RefundRequestCreateManyArgs>(args?: Prisma.SelectSubset<T, RefundRequestCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many RefundRequests and returns the data saved in the database.
   * @param {RefundRequestCreateManyAndReturnArgs} args - Arguments to create many RefundRequests.
   * @example
   * // Create many RefundRequests
   * const refundRequest = await prisma.refundRequest.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many RefundRequests and only return the `id`
   * const refundRequestWithIdOnly = await prisma.refundRequest.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends RefundRequestCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, RefundRequestCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a RefundRequest.
   * @param {RefundRequestDeleteArgs} args - Arguments to delete one RefundRequest.
   * @example
   * // Delete one RefundRequest
   * const RefundRequest = await prisma.refundRequest.delete({
   *   where: {
   *     // ... filter to delete one RefundRequest
   *   }
   * })
   * 
   */
  delete<T extends RefundRequestDeleteArgs>(args: Prisma.SelectSubset<T, RefundRequestDeleteArgs<ExtArgs>>): Prisma.Prisma__RefundRequestClient<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one RefundRequest.
   * @param {RefundRequestUpdateArgs} args - Arguments to update one RefundRequest.
   * @example
   * // Update one RefundRequest
   * const refundRequest = await prisma.refundRequest.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends RefundRequestUpdateArgs>(args: Prisma.SelectSubset<T, RefundRequestUpdateArgs<ExtArgs>>): Prisma.Prisma__RefundRequestClient<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more RefundRequests.
   * @param {RefundRequestDeleteManyArgs} args - Arguments to filter RefundRequests to delete.
   * @example
   * // Delete a few RefundRequests
   * const { count } = await prisma.refundRequest.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends RefundRequestDeleteManyArgs>(args?: Prisma.SelectSubset<T, RefundRequestDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more RefundRequests.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RefundRequestUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many RefundRequests
   * const refundRequest = await prisma.refundRequest.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends RefundRequestUpdateManyArgs>(args: Prisma.SelectSubset<T, RefundRequestUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more RefundRequests and returns the data updated in the database.
   * @param {RefundRequestUpdateManyAndReturnArgs} args - Arguments to update many RefundRequests.
   * @example
   * // Update many RefundRequests
   * const refundRequest = await prisma.refundRequest.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more RefundRequests and only return the `id`
   * const refundRequestWithIdOnly = await prisma.refundRequest.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends RefundRequestUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, RefundRequestUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one RefundRequest.
   * @param {RefundRequestUpsertArgs} args - Arguments to update or create a RefundRequest.
   * @example
   * // Update or create a RefundRequest
   * const refundRequest = await prisma.refundRequest.upsert({
   *   create: {
   *     // ... data to create a RefundRequest
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the RefundRequest we want to update
   *   }
   * })
   */
  upsert<T extends RefundRequestUpsertArgs>(args: Prisma.SelectSubset<T, RefundRequestUpsertArgs<ExtArgs>>): Prisma.Prisma__RefundRequestClient<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of RefundRequests.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RefundRequestCountArgs} args - Arguments to filter RefundRequests to count.
   * @example
   * // Count the number of RefundRequests
   * const count = await prisma.refundRequest.count({
   *   where: {
   *     // ... the filter for the RefundRequests we want to count
   *   }
   * })
  **/
  count<T extends RefundRequestCountArgs>(
    args?: Prisma.Subset<T, RefundRequestCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], RefundRequestCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a RefundRequest.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RefundRequestAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends RefundRequestAggregateArgs>(args: Prisma.Subset<T, RefundRequestAggregateArgs>): Prisma.PrismaPromise<GetRefundRequestAggregateType<T>>

  /**
   * Group by RefundRequest.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RefundRequestGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends RefundRequestGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: RefundRequestGroupByArgs['orderBy'] }
      : { orderBy?: RefundRequestGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, RefundRequestGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRefundRequestGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the RefundRequest model
 */
readonly fields: RefundRequestFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for RefundRequest.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__RefundRequestClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  license<T extends Prisma.LicenseDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.LicenseDefaultArgs<ExtArgs>>): Prisma.Prisma__LicenseClient<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the RefundRequest model
 */
export interface RefundRequestFieldRefs {
  readonly id: Prisma.FieldRef<"RefundRequest", 'String'>
  readonly licenseId: Prisma.FieldRef<"RefundRequest", 'String'>
  readonly requestedBy: Prisma.FieldRef<"RefundRequest", 'String'>
  readonly reason: Prisma.FieldRef<"RefundRequest", 'String'>
  readonly status: Prisma.FieldRef<"RefundRequest", 'RefundStatus'>
  readonly amount: Prisma.FieldRef<"RefundRequest", 'Int'>
  readonly adminNotes: Prisma.FieldRef<"RefundRequest", 'String'>
  readonly processedBy: Prisma.FieldRef<"RefundRequest", 'String'>
  readonly processedAt: Prisma.FieldRef<"RefundRequest", 'DateTime'>
  readonly createdAt: Prisma.FieldRef<"RefundRequest", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"RefundRequest", 'DateTime'>
}
    

// Custom InputTypes
/**
 * RefundRequest findUnique
 */
export type RefundRequestFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestInclude<ExtArgs> | null
  /**
   * Filter, which RefundRequest to fetch.
   */
  where: Prisma.RefundRequestWhereUniqueInput
}

/**
 * RefundRequest findUniqueOrThrow
 */
export type RefundRequestFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestInclude<ExtArgs> | null
  /**
   * Filter, which RefundRequest to fetch.
   */
  where: Prisma.RefundRequestWhereUniqueInput
}

/**
 * RefundRequest findFirst
 */
export type RefundRequestFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestInclude<ExtArgs> | null
  /**
   * Filter, which RefundRequest to fetch.
   */
  where?: Prisma.RefundRequestWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RefundRequests to fetch.
   */
  orderBy?: Prisma.RefundRequestOrderByWithRelationInput | Prisma.RefundRequestOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for RefundRequests.
   */
  cursor?: Prisma.RefundRequestWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RefundRequests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RefundRequests.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of RefundRequests.
   */
  distinct?: Prisma.RefundRequestScalarFieldEnum | Prisma.RefundRequestScalarFieldEnum[]
}

/**
 * RefundRequest findFirstOrThrow
 */
export type RefundRequestFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestInclude<ExtArgs> | null
  /**
   * Filter, which RefundRequest to fetch.
   */
  where?: Prisma.RefundRequestWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RefundRequests to fetch.
   */
  orderBy?: Prisma.RefundRequestOrderByWithRelationInput | Prisma.RefundRequestOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for RefundRequests.
   */
  cursor?: Prisma.RefundRequestWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RefundRequests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RefundRequests.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of RefundRequests.
   */
  distinct?: Prisma.RefundRequestScalarFieldEnum | Prisma.RefundRequestScalarFieldEnum[]
}

/**
 * RefundRequest findMany
 */
export type RefundRequestFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestInclude<ExtArgs> | null
  /**
   * Filter, which RefundRequests to fetch.
   */
  where?: Prisma.RefundRequestWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RefundRequests to fetch.
   */
  orderBy?: Prisma.RefundRequestOrderByWithRelationInput | Prisma.RefundRequestOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing RefundRequests.
   */
  cursor?: Prisma.RefundRequestWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RefundRequests from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RefundRequests.
   */
  skip?: number
  distinct?: Prisma.RefundRequestScalarFieldEnum | Prisma.RefundRequestScalarFieldEnum[]
}

/**
 * RefundRequest create
 */
export type RefundRequestCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestInclude<ExtArgs> | null
  /**
   * The data needed to create a RefundRequest.
   */
  data: Prisma.XOR<Prisma.RefundRequestCreateInput, Prisma.RefundRequestUncheckedCreateInput>
}

/**
 * RefundRequest createMany
 */
export type RefundRequestCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many RefundRequests.
   */
  data: Prisma.RefundRequestCreateManyInput | Prisma.RefundRequestCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * RefundRequest createManyAndReturn
 */
export type RefundRequestCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * The data used to create many RefundRequests.
   */
  data: Prisma.RefundRequestCreateManyInput | Prisma.RefundRequestCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * RefundRequest update
 */
export type RefundRequestUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestInclude<ExtArgs> | null
  /**
   * The data needed to update a RefundRequest.
   */
  data: Prisma.XOR<Prisma.RefundRequestUpdateInput, Prisma.RefundRequestUncheckedUpdateInput>
  /**
   * Choose, which RefundRequest to update.
   */
  where: Prisma.RefundRequestWhereUniqueInput
}

/**
 * RefundRequest updateMany
 */
export type RefundRequestUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update RefundRequests.
   */
  data: Prisma.XOR<Prisma.RefundRequestUpdateManyMutationInput, Prisma.RefundRequestUncheckedUpdateManyInput>
  /**
   * Filter which RefundRequests to update
   */
  where?: Prisma.RefundRequestWhereInput
  /**
   * Limit how many RefundRequests to update.
   */
  limit?: number
}

/**
 * RefundRequest updateManyAndReturn
 */
export type RefundRequestUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * The data used to update RefundRequests.
   */
  data: Prisma.XOR<Prisma.RefundRequestUpdateManyMutationInput, Prisma.RefundRequestUncheckedUpdateManyInput>
  /**
   * Filter which RefundRequests to update
   */
  where?: Prisma.RefundRequestWhereInput
  /**
   * Limit how many RefundRequests to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * RefundRequest upsert
 */
export type RefundRequestUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestInclude<ExtArgs> | null
  /**
   * The filter to search for the RefundRequest to update in case it exists.
   */
  where: Prisma.RefundRequestWhereUniqueInput
  /**
   * In case the RefundRequest found by the `where` argument doesn't exist, create a new RefundRequest with this data.
   */
  create: Prisma.XOR<Prisma.RefundRequestCreateInput, Prisma.RefundRequestUncheckedCreateInput>
  /**
   * In case the RefundRequest was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.RefundRequestUpdateInput, Prisma.RefundRequestUncheckedUpdateInput>
}

/**
 * RefundRequest delete
 */
export type RefundRequestDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestInclude<ExtArgs> | null
  /**
   * Filter which RefundRequest to delete.
   */
  where: Prisma.RefundRequestWhereUniqueInput
}

/**
 * RefundRequest deleteMany
 */
export type RefundRequestDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which RefundRequests to delete
   */
  where?: Prisma.RefundRequestWhereInput
  /**
   * Limit how many RefundRequests to delete.
   */
  limit?: number
}

/**
 * RefundRequest without action
 */
export type RefundRequestDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestInclude<ExtArgs> | null
}
