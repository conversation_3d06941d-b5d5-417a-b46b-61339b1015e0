
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `AuditLog` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model AuditLog
 * 
 */
export type AuditLogModel = runtime.Types.Result.DefaultSelection<Prisma.$AuditLogPayload>

export type AggregateAuditLog = {
  _count: AuditLogCountAggregateOutputType | null
  _min: AuditLogMinAggregateOutputType | null
  _max: AuditLogMaxAggregateOutputType | null
}

export type AuditLogMinAggregateOutputType = {
  id: string | null
  action: $Enums.AuditAction | null
  licenseKey: string | null
  deviceHash: string | null
  userId: string | null
  targetUserId: string | null
  ipAddress: string | null
  userAgent: string | null
  createdAt: Date | null
}

export type AuditLogMaxAggregateOutputType = {
  id: string | null
  action: $Enums.AuditAction | null
  licenseKey: string | null
  deviceHash: string | null
  userId: string | null
  targetUserId: string | null
  ipAddress: string | null
  userAgent: string | null
  createdAt: Date | null
}

export type AuditLogCountAggregateOutputType = {
  id: number
  action: number
  licenseKey: number
  deviceHash: number
  userId: number
  targetUserId: number
  ipAddress: number
  userAgent: number
  details: number
  createdAt: number
  _all: number
}


export type AuditLogMinAggregateInputType = {
  id?: true
  action?: true
  licenseKey?: true
  deviceHash?: true
  userId?: true
  targetUserId?: true
  ipAddress?: true
  userAgent?: true
  createdAt?: true
}

export type AuditLogMaxAggregateInputType = {
  id?: true
  action?: true
  licenseKey?: true
  deviceHash?: true
  userId?: true
  targetUserId?: true
  ipAddress?: true
  userAgent?: true
  createdAt?: true
}

export type AuditLogCountAggregateInputType = {
  id?: true
  action?: true
  licenseKey?: true
  deviceHash?: true
  userId?: true
  targetUserId?: true
  ipAddress?: true
  userAgent?: true
  details?: true
  createdAt?: true
  _all?: true
}

export type AuditLogAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which AuditLog to aggregate.
   */
  where?: Prisma.AuditLogWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AuditLogs to fetch.
   */
  orderBy?: Prisma.AuditLogOrderByWithRelationInput | Prisma.AuditLogOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.AuditLogWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AuditLogs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AuditLogs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned AuditLogs
  **/
  _count?: true | AuditLogCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: AuditLogMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: AuditLogMaxAggregateInputType
}

export type GetAuditLogAggregateType<T extends AuditLogAggregateArgs> = {
      [P in keyof T & keyof AggregateAuditLog]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateAuditLog[P]>
    : Prisma.GetScalarType<T[P], AggregateAuditLog[P]>
}




export type AuditLogGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AuditLogWhereInput
  orderBy?: Prisma.AuditLogOrderByWithAggregationInput | Prisma.AuditLogOrderByWithAggregationInput[]
  by: Prisma.AuditLogScalarFieldEnum[] | Prisma.AuditLogScalarFieldEnum
  having?: Prisma.AuditLogScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: AuditLogCountAggregateInputType | true
  _min?: AuditLogMinAggregateInputType
  _max?: AuditLogMaxAggregateInputType
}

export type AuditLogGroupByOutputType = {
  id: string
  action: $Enums.AuditAction
  licenseKey: string | null
  deviceHash: string | null
  userId: string | null
  targetUserId: string | null
  ipAddress: string | null
  userAgent: string | null
  details: runtime.JsonValue | null
  createdAt: Date
  _count: AuditLogCountAggregateOutputType | null
  _min: AuditLogMinAggregateOutputType | null
  _max: AuditLogMaxAggregateOutputType | null
}

type GetAuditLogGroupByPayload<T extends AuditLogGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<AuditLogGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof AuditLogGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], AuditLogGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], AuditLogGroupByOutputType[P]>
      }
    >
  >



export type AuditLogWhereInput = {
  AND?: Prisma.AuditLogWhereInput | Prisma.AuditLogWhereInput[]
  OR?: Prisma.AuditLogWhereInput[]
  NOT?: Prisma.AuditLogWhereInput | Prisma.AuditLogWhereInput[]
  id?: Prisma.StringFilter<"AuditLog"> | string
  action?: Prisma.EnumAuditActionFilter<"AuditLog"> | $Enums.AuditAction
  licenseKey?: Prisma.StringNullableFilter<"AuditLog"> | string | null
  deviceHash?: Prisma.StringNullableFilter<"AuditLog"> | string | null
  userId?: Prisma.StringNullableFilter<"AuditLog"> | string | null
  targetUserId?: Prisma.StringNullableFilter<"AuditLog"> | string | null
  ipAddress?: Prisma.StringNullableFilter<"AuditLog"> | string | null
  userAgent?: Prisma.StringNullableFilter<"AuditLog"> | string | null
  details?: Prisma.JsonNullableFilter<"AuditLog">
  createdAt?: Prisma.DateTimeFilter<"AuditLog"> | Date | string
}

export type AuditLogOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  action?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrderInput | Prisma.SortOrder
  deviceHash?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  targetUserId?: Prisma.SortOrderInput | Prisma.SortOrder
  ipAddress?: Prisma.SortOrderInput | Prisma.SortOrder
  userAgent?: Prisma.SortOrderInput | Prisma.SortOrder
  details?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type AuditLogWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.AuditLogWhereInput | Prisma.AuditLogWhereInput[]
  OR?: Prisma.AuditLogWhereInput[]
  NOT?: Prisma.AuditLogWhereInput | Prisma.AuditLogWhereInput[]
  action?: Prisma.EnumAuditActionFilter<"AuditLog"> | $Enums.AuditAction
  licenseKey?: Prisma.StringNullableFilter<"AuditLog"> | string | null
  deviceHash?: Prisma.StringNullableFilter<"AuditLog"> | string | null
  userId?: Prisma.StringNullableFilter<"AuditLog"> | string | null
  targetUserId?: Prisma.StringNullableFilter<"AuditLog"> | string | null
  ipAddress?: Prisma.StringNullableFilter<"AuditLog"> | string | null
  userAgent?: Prisma.StringNullableFilter<"AuditLog"> | string | null
  details?: Prisma.JsonNullableFilter<"AuditLog">
  createdAt?: Prisma.DateTimeFilter<"AuditLog"> | Date | string
}, "id">

export type AuditLogOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  action?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrderInput | Prisma.SortOrder
  deviceHash?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrderInput | Prisma.SortOrder
  targetUserId?: Prisma.SortOrderInput | Prisma.SortOrder
  ipAddress?: Prisma.SortOrderInput | Prisma.SortOrder
  userAgent?: Prisma.SortOrderInput | Prisma.SortOrder
  details?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.AuditLogCountOrderByAggregateInput
  _max?: Prisma.AuditLogMaxOrderByAggregateInput
  _min?: Prisma.AuditLogMinOrderByAggregateInput
}

export type AuditLogScalarWhereWithAggregatesInput = {
  AND?: Prisma.AuditLogScalarWhereWithAggregatesInput | Prisma.AuditLogScalarWhereWithAggregatesInput[]
  OR?: Prisma.AuditLogScalarWhereWithAggregatesInput[]
  NOT?: Prisma.AuditLogScalarWhereWithAggregatesInput | Prisma.AuditLogScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"AuditLog"> | string
  action?: Prisma.EnumAuditActionWithAggregatesFilter<"AuditLog"> | $Enums.AuditAction
  licenseKey?: Prisma.StringNullableWithAggregatesFilter<"AuditLog"> | string | null
  deviceHash?: Prisma.StringNullableWithAggregatesFilter<"AuditLog"> | string | null
  userId?: Prisma.StringNullableWithAggregatesFilter<"AuditLog"> | string | null
  targetUserId?: Prisma.StringNullableWithAggregatesFilter<"AuditLog"> | string | null
  ipAddress?: Prisma.StringNullableWithAggregatesFilter<"AuditLog"> | string | null
  userAgent?: Prisma.StringNullableWithAggregatesFilter<"AuditLog"> | string | null
  details?: Prisma.JsonNullableWithAggregatesFilter<"AuditLog">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"AuditLog"> | Date | string
}

export type AuditLogCreateInput = {
  id?: string
  action: $Enums.AuditAction
  licenseKey?: string | null
  deviceHash?: string | null
  userId?: string | null
  targetUserId?: string | null
  ipAddress?: string | null
  userAgent?: string | null
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type AuditLogUncheckedCreateInput = {
  id?: string
  action: $Enums.AuditAction
  licenseKey?: string | null
  deviceHash?: string | null
  userId?: string | null
  targetUserId?: string | null
  ipAddress?: string | null
  userAgent?: string | null
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type AuditLogUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.EnumAuditActionFieldUpdateOperationsInput | $Enums.AuditAction
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceHash?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  targetUserId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AuditLogUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.EnumAuditActionFieldUpdateOperationsInput | $Enums.AuditAction
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceHash?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  targetUserId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AuditLogCreateManyInput = {
  id?: string
  action: $Enums.AuditAction
  licenseKey?: string | null
  deviceHash?: string | null
  userId?: string | null
  targetUserId?: string | null
  ipAddress?: string | null
  userAgent?: string | null
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type AuditLogUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.EnumAuditActionFieldUpdateOperationsInput | $Enums.AuditAction
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceHash?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  targetUserId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AuditLogUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.EnumAuditActionFieldUpdateOperationsInput | $Enums.AuditAction
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceHash?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  targetUserId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  details?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AuditLogCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  action?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrder
  deviceHash?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  targetUserId?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  details?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type AuditLogMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  action?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrder
  deviceHash?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  targetUserId?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type AuditLogMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  action?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrder
  deviceHash?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  targetUserId?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type EnumAuditActionFieldUpdateOperationsInput = {
  set?: $Enums.AuditAction
}



export type AuditLogSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  action?: boolean
  licenseKey?: boolean
  deviceHash?: boolean
  userId?: boolean
  targetUserId?: boolean
  ipAddress?: boolean
  userAgent?: boolean
  details?: boolean
  createdAt?: boolean
}, ExtArgs["result"]["auditLog"]>

export type AuditLogSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  action?: boolean
  licenseKey?: boolean
  deviceHash?: boolean
  userId?: boolean
  targetUserId?: boolean
  ipAddress?: boolean
  userAgent?: boolean
  details?: boolean
  createdAt?: boolean
}, ExtArgs["result"]["auditLog"]>

export type AuditLogSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  action?: boolean
  licenseKey?: boolean
  deviceHash?: boolean
  userId?: boolean
  targetUserId?: boolean
  ipAddress?: boolean
  userAgent?: boolean
  details?: boolean
  createdAt?: boolean
}, ExtArgs["result"]["auditLog"]>

export type AuditLogSelectScalar = {
  id?: boolean
  action?: boolean
  licenseKey?: boolean
  deviceHash?: boolean
  userId?: boolean
  targetUserId?: boolean
  ipAddress?: boolean
  userAgent?: boolean
  details?: boolean
  createdAt?: boolean
}

export type AuditLogOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "action" | "licenseKey" | "deviceHash" | "userId" | "targetUserId" | "ipAddress" | "userAgent" | "details" | "createdAt", ExtArgs["result"]["auditLog"]>

export type $AuditLogPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "AuditLog"
  objects: {}
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    action: $Enums.AuditAction
    licenseKey: string | null
    deviceHash: string | null
    userId: string | null
    targetUserId: string | null
    ipAddress: string | null
    userAgent: string | null
    details: runtime.JsonValue | null
    createdAt: Date
  }, ExtArgs["result"]["auditLog"]>
  composites: {}
}

export type AuditLogGetPayload<S extends boolean | null | undefined | AuditLogDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$AuditLogPayload, S>

export type AuditLogCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<AuditLogFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: AuditLogCountAggregateInputType | true
  }

export interface AuditLogDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['AuditLog'], meta: { name: 'AuditLog' } }
  /**
   * Find zero or one AuditLog that matches the filter.
   * @param {AuditLogFindUniqueArgs} args - Arguments to find a AuditLog
   * @example
   * // Get one AuditLog
   * const auditLog = await prisma.auditLog.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends AuditLogFindUniqueArgs>(args: Prisma.SelectSubset<T, AuditLogFindUniqueArgs<ExtArgs>>): Prisma.Prisma__AuditLogClient<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one AuditLog that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {AuditLogFindUniqueOrThrowArgs} args - Arguments to find a AuditLog
   * @example
   * // Get one AuditLog
   * const auditLog = await prisma.auditLog.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends AuditLogFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, AuditLogFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__AuditLogClient<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first AuditLog that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AuditLogFindFirstArgs} args - Arguments to find a AuditLog
   * @example
   * // Get one AuditLog
   * const auditLog = await prisma.auditLog.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends AuditLogFindFirstArgs>(args?: Prisma.SelectSubset<T, AuditLogFindFirstArgs<ExtArgs>>): Prisma.Prisma__AuditLogClient<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first AuditLog that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AuditLogFindFirstOrThrowArgs} args - Arguments to find a AuditLog
   * @example
   * // Get one AuditLog
   * const auditLog = await prisma.auditLog.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends AuditLogFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, AuditLogFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__AuditLogClient<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more AuditLogs that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AuditLogFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all AuditLogs
   * const auditLogs = await prisma.auditLog.findMany()
   * 
   * // Get first 10 AuditLogs
   * const auditLogs = await prisma.auditLog.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const auditLogWithIdOnly = await prisma.auditLog.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends AuditLogFindManyArgs>(args?: Prisma.SelectSubset<T, AuditLogFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a AuditLog.
   * @param {AuditLogCreateArgs} args - Arguments to create a AuditLog.
   * @example
   * // Create one AuditLog
   * const AuditLog = await prisma.auditLog.create({
   *   data: {
   *     // ... data to create a AuditLog
   *   }
   * })
   * 
   */
  create<T extends AuditLogCreateArgs>(args: Prisma.SelectSubset<T, AuditLogCreateArgs<ExtArgs>>): Prisma.Prisma__AuditLogClient<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many AuditLogs.
   * @param {AuditLogCreateManyArgs} args - Arguments to create many AuditLogs.
   * @example
   * // Create many AuditLogs
   * const auditLog = await prisma.auditLog.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends AuditLogCreateManyArgs>(args?: Prisma.SelectSubset<T, AuditLogCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many AuditLogs and returns the data saved in the database.
   * @param {AuditLogCreateManyAndReturnArgs} args - Arguments to create many AuditLogs.
   * @example
   * // Create many AuditLogs
   * const auditLog = await prisma.auditLog.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many AuditLogs and only return the `id`
   * const auditLogWithIdOnly = await prisma.auditLog.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends AuditLogCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, AuditLogCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a AuditLog.
   * @param {AuditLogDeleteArgs} args - Arguments to delete one AuditLog.
   * @example
   * // Delete one AuditLog
   * const AuditLog = await prisma.auditLog.delete({
   *   where: {
   *     // ... filter to delete one AuditLog
   *   }
   * })
   * 
   */
  delete<T extends AuditLogDeleteArgs>(args: Prisma.SelectSubset<T, AuditLogDeleteArgs<ExtArgs>>): Prisma.Prisma__AuditLogClient<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one AuditLog.
   * @param {AuditLogUpdateArgs} args - Arguments to update one AuditLog.
   * @example
   * // Update one AuditLog
   * const auditLog = await prisma.auditLog.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends AuditLogUpdateArgs>(args: Prisma.SelectSubset<T, AuditLogUpdateArgs<ExtArgs>>): Prisma.Prisma__AuditLogClient<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more AuditLogs.
   * @param {AuditLogDeleteManyArgs} args - Arguments to filter AuditLogs to delete.
   * @example
   * // Delete a few AuditLogs
   * const { count } = await prisma.auditLog.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends AuditLogDeleteManyArgs>(args?: Prisma.SelectSubset<T, AuditLogDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more AuditLogs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AuditLogUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many AuditLogs
   * const auditLog = await prisma.auditLog.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends AuditLogUpdateManyArgs>(args: Prisma.SelectSubset<T, AuditLogUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more AuditLogs and returns the data updated in the database.
   * @param {AuditLogUpdateManyAndReturnArgs} args - Arguments to update many AuditLogs.
   * @example
   * // Update many AuditLogs
   * const auditLog = await prisma.auditLog.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more AuditLogs and only return the `id`
   * const auditLogWithIdOnly = await prisma.auditLog.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends AuditLogUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, AuditLogUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one AuditLog.
   * @param {AuditLogUpsertArgs} args - Arguments to update or create a AuditLog.
   * @example
   * // Update or create a AuditLog
   * const auditLog = await prisma.auditLog.upsert({
   *   create: {
   *     // ... data to create a AuditLog
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the AuditLog we want to update
   *   }
   * })
   */
  upsert<T extends AuditLogUpsertArgs>(args: Prisma.SelectSubset<T, AuditLogUpsertArgs<ExtArgs>>): Prisma.Prisma__AuditLogClient<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of AuditLogs.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AuditLogCountArgs} args - Arguments to filter AuditLogs to count.
   * @example
   * // Count the number of AuditLogs
   * const count = await prisma.auditLog.count({
   *   where: {
   *     // ... the filter for the AuditLogs we want to count
   *   }
   * })
  **/
  count<T extends AuditLogCountArgs>(
    args?: Prisma.Subset<T, AuditLogCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], AuditLogCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a AuditLog.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AuditLogAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends AuditLogAggregateArgs>(args: Prisma.Subset<T, AuditLogAggregateArgs>): Prisma.PrismaPromise<GetAuditLogAggregateType<T>>

  /**
   * Group by AuditLog.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AuditLogGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends AuditLogGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: AuditLogGroupByArgs['orderBy'] }
      : { orderBy?: AuditLogGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, AuditLogGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAuditLogGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the AuditLog model
 */
readonly fields: AuditLogFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for AuditLog.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__AuditLogClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the AuditLog model
 */
export interface AuditLogFieldRefs {
  readonly id: Prisma.FieldRef<"AuditLog", 'String'>
  readonly action: Prisma.FieldRef<"AuditLog", 'AuditAction'>
  readonly licenseKey: Prisma.FieldRef<"AuditLog", 'String'>
  readonly deviceHash: Prisma.FieldRef<"AuditLog", 'String'>
  readonly userId: Prisma.FieldRef<"AuditLog", 'String'>
  readonly targetUserId: Prisma.FieldRef<"AuditLog", 'String'>
  readonly ipAddress: Prisma.FieldRef<"AuditLog", 'String'>
  readonly userAgent: Prisma.FieldRef<"AuditLog", 'String'>
  readonly details: Prisma.FieldRef<"AuditLog", 'Json'>
  readonly createdAt: Prisma.FieldRef<"AuditLog", 'DateTime'>
}
    

// Custom InputTypes
/**
 * AuditLog findUnique
 */
export type AuditLogFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * Filter, which AuditLog to fetch.
   */
  where: Prisma.AuditLogWhereUniqueInput
}

/**
 * AuditLog findUniqueOrThrow
 */
export type AuditLogFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * Filter, which AuditLog to fetch.
   */
  where: Prisma.AuditLogWhereUniqueInput
}

/**
 * AuditLog findFirst
 */
export type AuditLogFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * Filter, which AuditLog to fetch.
   */
  where?: Prisma.AuditLogWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AuditLogs to fetch.
   */
  orderBy?: Prisma.AuditLogOrderByWithRelationInput | Prisma.AuditLogOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for AuditLogs.
   */
  cursor?: Prisma.AuditLogWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AuditLogs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AuditLogs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of AuditLogs.
   */
  distinct?: Prisma.AuditLogScalarFieldEnum | Prisma.AuditLogScalarFieldEnum[]
}

/**
 * AuditLog findFirstOrThrow
 */
export type AuditLogFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * Filter, which AuditLog to fetch.
   */
  where?: Prisma.AuditLogWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AuditLogs to fetch.
   */
  orderBy?: Prisma.AuditLogOrderByWithRelationInput | Prisma.AuditLogOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for AuditLogs.
   */
  cursor?: Prisma.AuditLogWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AuditLogs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AuditLogs.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of AuditLogs.
   */
  distinct?: Prisma.AuditLogScalarFieldEnum | Prisma.AuditLogScalarFieldEnum[]
}

/**
 * AuditLog findMany
 */
export type AuditLogFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * Filter, which AuditLogs to fetch.
   */
  where?: Prisma.AuditLogWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AuditLogs to fetch.
   */
  orderBy?: Prisma.AuditLogOrderByWithRelationInput | Prisma.AuditLogOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing AuditLogs.
   */
  cursor?: Prisma.AuditLogWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AuditLogs from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AuditLogs.
   */
  skip?: number
  distinct?: Prisma.AuditLogScalarFieldEnum | Prisma.AuditLogScalarFieldEnum[]
}

/**
 * AuditLog create
 */
export type AuditLogCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * The data needed to create a AuditLog.
   */
  data: Prisma.XOR<Prisma.AuditLogCreateInput, Prisma.AuditLogUncheckedCreateInput>
}

/**
 * AuditLog createMany
 */
export type AuditLogCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many AuditLogs.
   */
  data: Prisma.AuditLogCreateManyInput | Prisma.AuditLogCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * AuditLog createManyAndReturn
 */
export type AuditLogCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * The data used to create many AuditLogs.
   */
  data: Prisma.AuditLogCreateManyInput | Prisma.AuditLogCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * AuditLog update
 */
export type AuditLogUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * The data needed to update a AuditLog.
   */
  data: Prisma.XOR<Prisma.AuditLogUpdateInput, Prisma.AuditLogUncheckedUpdateInput>
  /**
   * Choose, which AuditLog to update.
   */
  where: Prisma.AuditLogWhereUniqueInput
}

/**
 * AuditLog updateMany
 */
export type AuditLogUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update AuditLogs.
   */
  data: Prisma.XOR<Prisma.AuditLogUpdateManyMutationInput, Prisma.AuditLogUncheckedUpdateManyInput>
  /**
   * Filter which AuditLogs to update
   */
  where?: Prisma.AuditLogWhereInput
  /**
   * Limit how many AuditLogs to update.
   */
  limit?: number
}

/**
 * AuditLog updateManyAndReturn
 */
export type AuditLogUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * The data used to update AuditLogs.
   */
  data: Prisma.XOR<Prisma.AuditLogUpdateManyMutationInput, Prisma.AuditLogUncheckedUpdateManyInput>
  /**
   * Filter which AuditLogs to update
   */
  where?: Prisma.AuditLogWhereInput
  /**
   * Limit how many AuditLogs to update.
   */
  limit?: number
}

/**
 * AuditLog upsert
 */
export type AuditLogUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * The filter to search for the AuditLog to update in case it exists.
   */
  where: Prisma.AuditLogWhereUniqueInput
  /**
   * In case the AuditLog found by the `where` argument doesn't exist, create a new AuditLog with this data.
   */
  create: Prisma.XOR<Prisma.AuditLogCreateInput, Prisma.AuditLogUncheckedCreateInput>
  /**
   * In case the AuditLog was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.AuditLogUpdateInput, Prisma.AuditLogUncheckedUpdateInput>
}

/**
 * AuditLog delete
 */
export type AuditLogDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * Filter which AuditLog to delete.
   */
  where: Prisma.AuditLogWhereUniqueInput
}

/**
 * AuditLog deleteMany
 */
export type AuditLogDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which AuditLogs to delete
   */
  where?: Prisma.AuditLogWhereInput
  /**
   * Limit how many AuditLogs to delete.
   */
  limit?: number
}

/**
 * AuditLog without action
 */
export type AuditLogDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
}
