model User {
  id            String    @id @default(cuid())
  name          String
  email         String    @unique
  emailVerified Boolean   @default(false)
  image         String?
  role          UserRole  @default(USER)
  isActive      Boolean   @default(true)
  invitedBy     String? // ID of user who invited this user
  invitedAt     DateTime? // When the user was invited
  lastLoginAt   DateTime? // Track last login for security
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relationships
  sessions           Session[]
  accounts           Account[]
  invitedUsers       UserInvitation[] @relation("InvitedBy")
  receivedInvitation UserInvitation?  @relation("InvitedUser", fields: [userInvitationId], references: [id])
  userInvitationId   String?          @unique

  @@index([role])
  @@index([isActive])
  @@index([email])
  @@map("user")
}

model Session {
  id        String   @id @default(cuid())
  expiresAt DateTime
  token     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

model Account {
  id                    String    @id @default(cuid())
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  @@map("account")
}

model Verification {
  id         String   @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("verification")
}

model UserInvitation {
  id          String           @id @default(cuid())
  email       String           @unique
  role        UserRole         @default(USER)
  token       String           @unique // Unique invitation token
  status      InvitationStatus @default(PENDING)
  invitedById String // User who sent the invitation
  invitedBy   User             @relation("InvitedBy", fields: [invitedById], references: [id], onDelete: Cascade)
  invitedUser User?            @relation("InvitedUser")
  expiresAt   DateTime // Invitation expiration
  acceptedAt  DateTime? // When invitation was accepted
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  @@index([email])
  @@index([token])
  @@index([status])
  @@index([expiresAt])
  @@map("user_invitations")
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  MANAGER
  USER
  VIEWER
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  REVOKED
}
