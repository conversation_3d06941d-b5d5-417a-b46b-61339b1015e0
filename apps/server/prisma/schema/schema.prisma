generator client {
  provider     = "prisma-client"
  output       = "../generated"
  moduleFormat = "esm"
}

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

model License {
  id          String      @id @default(cuid())
  licenseKey  String      @unique
  email       String
  licenseType LicenseType
  maxDevices  Int         @default(2)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  expiresAt   DateTime?

  // Payment tracking
  stripePaymentIntentId  String?
  upgradePaymentIntentId String[] @default([])

  // Refund tracking
  refundedAt     DateTime? // When the license was refunded
  refundReason   String? // Reason for refund
  refundAmount   Int? // Amount refunded in cents
  stripeRefundId String? // Stripe refund ID for tracking

  // Relationships
  devices       Device[]
  refundRequest RefundRequest?

  @@index([email])
  @@index([licenseKey])
  @@index([expiresAt])
  @@index([refundedAt])
  @@map("licenses")
}

model Device {
  id         String   @id @default(cuid())
  licenseId  String
  deviceHash String // Hashed device ID for security
  salt       String // Salt used for hashing
  firstSeen  DateTime @default(now())
  lastSeen   DateTime @default(now())
  appVersion String?
  isActive   Boolean  @default(true)

  // Enhanced device metadata for better user experience
  deviceName       String? // User-friendly device name (e.g., "John's MacBook Pro")
  deviceType       String? // Device type (e.g., "MacBook Pro", "iMac", "Mac Studio")
  deviceModel      String? // Model details (e.g., "14-inch", "M2", "2023")
  operatingSystem  String? // OS version (e.g., "macOS 14.1", "macOS Sonoma")
  architecture     String? // CPU architecture (e.g., "arm64", "x86_64")
  screenResolution String? // Display resolution (e.g., "3024x1964", "5120x2880")
  totalMemory      String? // RAM amount (e.g., "16 GB", "32 GB")

  // Optional user-provided metadata
  userNickname String? // Custom name set by user (e.g., "Work Laptop", "Home Desktop")
  location     String? // Optional location (e.g., "Office", "Home")
  notes        String? // User notes about the device

  // Relationships
  license License @relation(fields: [licenseId], references: [id], onDelete: Cascade)

  @@unique([licenseId, deviceHash])
  @@index([licenseId])
  @@index([deviceHash])
  @@map("devices")
}

model AuditLog {
  id           String      @id @default(cuid())
  action       AuditAction
  licenseKey   String?
  deviceHash   String?
  userId       String? // User who performed the action
  targetUserId String? // User who was affected by the action (for user management)
  ipAddress    String?
  userAgent    String?
  details      Json?
  createdAt    DateTime    @default(now())

  @@index([licenseKey])
  @@index([userId])
  @@index([targetUserId])
  @@index([action])
  @@index([createdAt])
  @@map("audit_logs")
}

model RefundRequest {
  id          String       @id @default(cuid())
  licenseId   String       @unique
  requestedBy String // Email of person requesting refund
  reason      String // Reason for refund request
  status      RefundStatus @default(PENDING)
  amount      Int? // Requested refund amount in cents
  adminNotes  String? // Internal admin notes
  processedBy String? // Admin who processed the request
  processedAt DateTime? // When the request was processed
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relationships
  license License @relation(fields: [licenseId], references: [id], onDelete: Cascade)

  @@index([status])
  @@index([createdAt])
  @@map("refund_requests")
}

enum LicenseType {
  trial
  standard
  extended
}

enum RefundStatus {
  PENDING
  APPROVED
  REJECTED
  PROCESSED
  FAILED
}

enum AuditAction {
  // License actions
  LICENSE_CREATED
  LICENSE_VALIDATED
  LICENSE_EXPIRED
  DEVICE_ADDED
  DEVICE_REMOVED
  SUSPICIOUS_ACTIVITY
  RATE_LIMIT_EXCEEDED

  // Refund actions
  REFUND_REQUESTED
  REFUND_APPROVED
  REFUND_REJECTED
  REFUND_PROCESSED
  REFUND_FAILED

  // User management actions
  USER_CREATED
  USER_UPDATED
  USER_ROLE_CHANGED
  USER_ACTIVATED
  USER_DEACTIVATED
  USER_DELETED
  USER_LOGIN
  USER_LOGOUT

  // Invitation actions
  INVITATION_SENT
  INVITATION_ACCEPTED
  INVITATION_EXPIRED
  INVITATION_REVOKED

  // Permission actions
  PERMISSION_GRANTED
  PERMISSION_REVOKED
  UNAUTHORIZED_ACCESS_ATTEMPT
}
