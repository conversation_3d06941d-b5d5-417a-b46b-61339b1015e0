/**
 * TanStack Query hooks for user management and invitations
 * Provides data fetching, caching, and mutation capabilities for the RBAC system
 */

import type {
	CreateUserRequest,
	ListUsersRequest,
	SendInvitationRequest,
	UpdateUserRequest,
} from "@snapback/shared";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { usersApi } from "@/lib/api";
import { getInvalidationKeys, queryKeys } from "@/lib/query-keys";

/**
 * Hook to get current user information and permissions
 */
export function useCurrentUser() {
	return useQuery({
		queryKey: queryKeys.users.currentUser(),
		queryFn: () => usersApi.getCurrentUser(),
		staleTime: 5 * 60 * 1000, // 5 minutes
		retry: (failureCount, error) => {
			// Don't retry on authentication errors
			if (
				error &&
				typeof error === "object" &&
				"status" in error &&
				error.status === 401
			) {
				return false;
			}
			return failureCount < 3;
		},
	});
}

/**
 * Hook to list users with pagination and filtering
 */
export function useUsers(params?: ListUsersRequest) {
	return useQuery({
		queryKey: queryKeys.users.list(params || {}),
		queryFn: () => usersApi.listUsers(params),
		staleTime: 2 * 60 * 1000, // 2 minutes
		enabled: true, // Always enabled, but will be controlled by permissions in the UI
	});
}

/**
 * Hook to list user invitations
 */
export function useInvitations(params?: {
	page?: number;
	limit?: number;
	status?: string;
}) {
	return useQuery({
		queryKey: queryKeys.invitations.list(params || {}),
		queryFn: () => usersApi.listInvitations(params),
		staleTime: 1 * 60 * 1000, // 1 minute
	});
}

/**
 * Mutation hook to create a new user
 */
export function useCreateUser() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (userData: CreateUserRequest) => usersApi.createUser(userData),
		onSuccess: (data) => {
			// Invalidate and refetch user-related queries
			queryClient.invalidateQueries({
				queryKey: getInvalidationKeys.onUserChange(),
			});

			toast.success("User created successfully", {
				description: `${data.user.name} has been added to the system.`,
			});
		},
		onError: (error) => {
			console.error("Failed to create user:", error);
			toast.error("Failed to create user", {
				description:
					error instanceof Error
						? error.message
						: "An unexpected error occurred",
			});
		},
	});
}

/**
 * Mutation hook to update user information
 */
export function useUpdateUser() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			userId,
			updateData,
		}: {
			userId: string;
			updateData: UpdateUserRequest;
		}) => usersApi.updateUser(userId, updateData),
		onSuccess: (data) => {
			// Invalidate and refetch user-related queries
			queryClient.invalidateQueries({
				queryKey: getInvalidationKeys.onUserChange(),
			});

			toast.success("User updated successfully", {
				description: `${data.user.name}'s information has been updated.`,
			});
		},
		onError: (error) => {
			console.error("Failed to update user:", error);
			toast.error("Failed to update user", {
				description:
					error instanceof Error
						? error.message
						: "An unexpected error occurred",
			});
		},
	});
}

/**
 * Mutation hook to toggle user active status
 */
export function useToggleUserStatus() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ userId, isActive }: { userId: string; isActive: boolean }) =>
			usersApi.toggleUserStatus(userId, isActive),
		onSuccess: (data, variables) => {
			// Invalidate and refetch user-related queries
			queryClient.invalidateQueries({
				queryKey: getInvalidationKeys.onUserChange(),
			});

			const action = variables.isActive ? "activated" : "deactivated";
			toast.success(`User ${action} successfully`, {
				description: `${data.user.name} has been ${action}.`,
			});
		},
		onError: (error) => {
			console.error("Failed to toggle user status:", error);
			toast.error("Failed to update user status", {
				description:
					error instanceof Error
						? error.message
						: "An unexpected error occurred",
			});
		},
	});
}

/**
 * Mutation hook to send user invitation
 */
export function useSendInvitation() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (invitationData: SendInvitationRequest) =>
			usersApi.sendInvitation(invitationData),
		onSuccess: (data) => {
			// Invalidate and refetch invitation-related queries
			queryClient.invalidateQueries({
				queryKey: getInvalidationKeys.onInvitationChange(),
			});

			toast.success("Invitation sent successfully", {
				description: `An invitation has been sent to ${data.invitation.email}.`,
			});
		},
		onError: (error) => {
			console.error("Failed to send invitation:", error);
			toast.error("Failed to send invitation", {
				description:
					error instanceof Error
						? error.message
						: "An unexpected error occurred",
			});
		},
	});
}

/**
 * Mutation hook to revoke user invitation
 */
export function useRevokeInvitation() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (invitationId: string) =>
			usersApi.revokeInvitation(invitationId),
		onSuccess: () => {
			// Invalidate and refetch invitation-related queries
			queryClient.invalidateQueries({
				queryKey: getInvalidationKeys.onInvitationChange(),
			});

			toast.success("Invitation revoked successfully", {
				description:
					"The invitation has been revoked and can no longer be used.",
			});
		},
		onError: (error) => {
			console.error("Failed to revoke invitation:", error);
			toast.error("Failed to revoke invitation", {
				description:
					error instanceof Error
						? error.message
						: "An unexpected error occurred",
			});
		},
	});
}

/**
 * Hook to check if current user has specific permissions
 */
export function useUserPermissions() {
	const { data: currentUser } = useCurrentUser();

	return {
		permissions: currentUser?.permissions,
		canManageUsers: currentUser?.permissions?.canManageUsers ?? false,
		canInviteUsers: currentUser?.permissions?.canInviteUsers ?? false,
		canViewUsers: currentUser?.permissions?.canViewUsers ?? false,
		canDeactivateUsers: currentUser?.permissions?.canDeactivateUsers ?? false,
		canManageRoles: currentUser?.permissions?.canManageRoles ?? false,
		assignableRoles: currentUser?.permissions?.assignableRoles ?? [],
		isLoading: !currentUser,
	};
}

/**
 * Hook to get user context with role-based filtering
 */
export function useUserContext() {
	const { data: currentUser, isLoading } = useCurrentUser();

	console.log("User context:", currentUser);

	return {
		user: currentUser?.user,
		permissions: currentUser?.permissions,
		trialInfo: currentUser?.trialInfo,
		isLoading,
		isAuthenticated: !!currentUser?.user,
	};
}
