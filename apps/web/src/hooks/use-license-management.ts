/**
 * TanStack Query hooks for license management with pagination
 * Provides data fetching, caching, and mutation capabilities for license operations
 */

import type {
	CreateLicenseRequest,
	License,
	LicenseStatusResponse,
	LicenseValidationResponse,
	ListLicensesRequest,
	ListLicensesResponse,
	UpdateDeviceMetadataRequest,
	UpgradeLicenseRequest,
	ValidateLicenseRequest,
} from "@snapback/shared";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { licenseApi } from "@/lib/api";
import { getInvalidationKeys, queryKeys } from "@/lib/query-keys";

/**
 * Hook to list licenses with pagination and filtering
 */
export function useLicenses(params?: ListLicensesRequest) {
	return useQuery({
		queryKey: queryKeys.licenses.list(params || {}),
		queryFn: () => licenseApi.list(params),
		staleTime: 2 * 60 * 1000, // 2 minutes
		enabled: true, // Always enabled, but will be controlled by permissions in the UI
	});
}

/**
 * Hook to get a specific license status
 */
export function useLicenseStatus(licenseKey: string) {
	return useQuery({
		queryKey: queryKeys.licenses.status(licenseKey),
		queryFn: () => licenseApi.getStatus(licenseKey),
		staleTime: 5 * 60 * 1000, // 5 minutes
		enabled: !!licenseKey,
	});
}

/**
 * Hook to create a new license
 */
export function useCreateLicense() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (licenseData: CreateLicenseRequest) =>
			licenseApi.create(licenseData),
		onSuccess: (data) => {
			// Invalidate and refetch license-related queries
			queryClient.invalidateQueries({
				queryKey: getInvalidationKeys.onLicenseChange(),
			});

			toast.success("License created successfully", {
				description: `License ${data.licenseKey} has been created for ${data.email}.`,
			});
		},
		onError: (error) => {
			console.error("Failed to create license:", error);
			toast.error("Failed to create license", {
				description:
					error instanceof Error
						? error.message
						: "An unexpected error occurred",
			});
		},
	});
}

/**
 * Hook to validate a license
 */
export function useValidateLicense() {
	return useMutation({
		mutationFn: (validationData: ValidateLicenseRequest) =>
			licenseApi.validate(validationData),
		onSuccess: (data) => {
			if (data.success) {
				toast.success("License validated successfully", {
					description: "The license is valid and active.",
				});
			} else {
				toast.error("License validation failed", {
					description: data.error || "The license is invalid or expired.",
				});
			}
		},
		onError: (error) => {
			console.error("Failed to validate license:", error);
			toast.error("Failed to validate license", {
				description:
					error instanceof Error
						? error.message
						: "An unexpected error occurred",
			});
		},
	});
}

/**
 * Hook to upgrade a license
 */
export function useUpgradeLicense() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (upgradeData: UpgradeLicenseRequest) =>
			licenseApi.upgrade(upgradeData),
		onSuccess: (data) => {
			// Invalidate and refetch license-related queries
			queryClient.invalidateQueries({
				queryKey: getInvalidationKeys.onLicenseChange(),
			});

			toast.success("License upgraded successfully", {
				description: `License ${data.licenseKey} has been upgraded.`,
			});
		},
		onError: (error) => {
			console.error("Failed to upgrade license:", error);
			toast.error("Failed to upgrade license", {
				description:
					error instanceof Error
						? error.message
						: "An unexpected error occurred",
			});
		},
	});
}

/**
 * Hook to resend license email
 */
export function useResendLicenseEmail() {
	return useMutation({
		mutationFn: (email: string) => licenseApi.resend(email),
		onSuccess: () => {
			toast.success("License email sent", {
				description: "The license email has been resent successfully.",
			});
		},
		onError: (error) => {
			console.error("Failed to resend license email:", error);
			toast.error("Failed to resend license email", {
				description:
					error instanceof Error
						? error.message
						: "An unexpected error occurred",
			});
		},
	});
}

/**
 * Hook to remove a device from a license
 */
export function useRemoveDevice() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ deviceId, token }: { deviceId: string; token: string }) =>
			licenseApi.removeDevice(deviceId, token),
		onSuccess: () => {
			// Invalidate and refetch license-related queries
			queryClient.invalidateQueries({
				queryKey: getInvalidationKeys.onLicenseChange(),
			});

			toast.success("Device removed successfully", {
				description: "The device has been removed from the license.",
			});
		},
		onError: (error) => {
			console.error("Failed to remove device:", error);
			toast.error("Failed to remove device", {
				description:
					error instanceof Error
						? error.message
						: "An unexpected error occurred",
			});
		},
	});
}

/**
 * Hook to update device metadata
 */
export function useUpdateDeviceMetadata() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			data,
			token,
		}: {
			data: UpdateDeviceMetadataRequest;
			token: string;
		}) => licenseApi.updateDeviceMetadata(data, token),
		onSuccess: () => {
			// Invalidate and refetch license-related queries
			queryClient.invalidateQueries({
				queryKey: getInvalidationKeys.onLicenseChange(),
			});

			toast.success("Device metadata updated", {
				description: "The device information has been updated successfully.",
			});
		},
		onError: (error) => {
			console.error("Failed to update device metadata:", error);
			toast.error("Failed to update device metadata", {
				description:
					error instanceof Error
						? error.message
						: "An unexpected error occurred",
			});
		},
	});
}
