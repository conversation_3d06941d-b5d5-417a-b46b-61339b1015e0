"use client";

import type { License, LicenseStatusResponse } from "@snapback/shared";
import {
	useInfiniteQuery,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { licenseApi } from "@/lib/api";
import { getInvalidationKeys, queryKeys } from "@/lib/query-keys";

interface LicensesPageData {
	licenses: License[];
	pagination: {
		page: number;
		limit: number;
		totalCount: number;
		totalPages: number;
		hasNextPage: boolean;
		hasPreviousPage: boolean;
	};
}

interface LicenseFilters {
	page?: number;
	limit?: number;
	type?: string;
	search?: string;
	status?: string;
}

// Helper function to fetch licenses data (can be used by both regular and infinite queries)
async function fetchLicensesData(
	filters: LicenseFilters = {},
): Promise<LicensesPageData> {
	// For now, return mock data since the endpoint doesn't exist yet
	// In production, this would be: return analyticsApi.getLicenses(filters);

	await new Promise((resolve) => setTimeout(resolve, 800));

	const mockLicenses: License[] = [
		{
			id: "1",
			licenseKey: "ABCD-1234-EFGH-5678-IJKL",
			email: "<EMAIL>",
			licenseType: "standard",
			maxDevices: 2,
			expiresAt: null,
			createdAt: "2024-01-15T10:30:00Z",
			updatedAt: "2024-01-15T10:30:00Z",
			devicesUsed: 1,
			devices: [],
		},
		{
			id: "2",
			licenseKey: "WXYZ-9876-ABCD-5432-EFGH",
			email: "<EMAIL>",
			licenseType: "extended",
			maxDevices: 5,
			expiresAt: null,
			createdAt: "2024-01-14T16:20:00Z",
			updatedAt: "2024-01-14T16:20:00Z",
			devicesUsed: 3,
			devices: [],
		},
		{
			id: "3",
			licenseKey: "MNOP-2468-QRST-1357-UVWX",
			email: "<EMAIL>",
			licenseType: "standard",
			maxDevices: 2,
			expiresAt: null,
			createdAt: "2024-01-13T09:45:00Z",
			updatedAt: "2024-01-13T09:45:00Z",
			devicesUsed: 2,
			devices: [],
			refundedAt: "2024-01-14T12:00:00Z",
			refundReason: "Customer requested refund",
			refundAmount: 499,
		},
		{
			id: "4",
			licenseKey: "PQRS-3579-TUVW-2468-XYZA",
			email: "<EMAIL>",
			licenseType: "extended",
			maxDevices: 5,
			expiresAt: null,
			createdAt: "2024-01-12T14:15:00Z",
			updatedAt: "2024-01-12T14:15:00Z",
			devicesUsed: 4,
			devices: [],
		},
		{
			id: "5",
			licenseKey: "BCDE-4680-FGHI-3579-JKLM",
			email: "<EMAIL>",
			licenseType: "standard",
			maxDevices: 2,
			expiresAt: null,
			createdAt: "2024-01-11T11:30:00Z",
			updatedAt: "2024-01-11T11:30:00Z",
			devicesUsed: 1,
			devices: [],
		},
	];

	// Apply filters
	let filteredLicenses = mockLicenses;

	// Search filter
	if (filters.search) {
		const searchTerm = filters.search.toLowerCase();
		filteredLicenses = filteredLicenses.filter(
			(license) =>
				license.email.toLowerCase().includes(searchTerm) ||
				license.licenseKey.toLowerCase().includes(searchTerm),
		);
	}

	// Type filter
	if (filters.type && filters.type !== "all") {
		filteredLicenses = filteredLicenses.filter(
			(license) => license.licenseType === filters.type,
		);
	}

	// Status filter
	if (filters.status && filters.status !== "all") {
		filteredLicenses = filteredLicenses.filter((license) => {
			if (filters.status === "active") {
				return (
					!license.refundedAt &&
					(!license.expiresAt || new Date(license.expiresAt) > new Date())
				);
			}
			if (filters.status === "refunded") {
				return !!license.refundedAt;
			}
			if (filters.status === "expired") {
				return license.expiresAt && new Date(license.expiresAt) <= new Date();
			}
			return true;
		});
	}

	// Pagination
	const page = filters.page || 1;
	const limit = filters.limit || 20;
	const startIndex = (page - 1) * limit;
	const endIndex = startIndex + limit;
	const paginatedLicenses = filteredLicenses.slice(startIndex, endIndex);

	return {
		licenses: paginatedLicenses,
		pagination: {
			page,
			limit,
			totalCount: filteredLicenses.length,
			totalPages: Math.ceil(filteredLicenses.length / limit),
			hasNextPage: endIndex < filteredLicenses.length,
			hasPreviousPage: page > 1,
		},
	};
}

// Licenses List Query
export function useLicenses(filters: LicenseFilters = {}) {
	return useQuery({
		queryKey: queryKeys.licenses.list(filters),
		queryFn: () => fetchLicensesData(filters),
		staleTime: 2 * 60 * 1000, // 2 minutes
		gcTime: 5 * 60 * 1000, // 5 minutes
	});
}

// Infinite Query for Licenses (useful for large datasets)
export function useLicensesInfinite(
	filters: Omit<LicenseFilters, "page"> = {},
) {
	return useInfiniteQuery({
		queryKey: [...queryKeys.licenses.lists(), "infinite", filters],
		queryFn: async ({ pageParam = 1 }) => {
			return fetchLicensesData({
				...filters,
				page: pageParam,
			});
		},
		getNextPageParam: (lastPage) => {
			return lastPage?.pagination.hasNextPage
				? lastPage.pagination.page + 1
				: undefined;
		},
		initialPageParam: 1,
		staleTime: 2 * 60 * 1000,
		gcTime: 5 * 60 * 1000,
	});
}

// Single License Query
export function useLicense(licenseKey: string) {
	return useQuery({
		queryKey: queryKeys.licenses.detail(licenseKey),
		queryFn: async (): Promise<LicenseStatusResponse> => {
			// In production, this would be: return licenseApi.getStatus(licenseKey);

			await new Promise((resolve) => setTimeout(resolve, 300));

			return {
				licenseKey,
				licenseType: "standard",
				email: "<EMAIL>",
				createdAt: "2024-01-15T10:30:00Z",
				expiresAt: null,
				maxDevices: 2,
				devicesUsed: 1,
				isExpired: false,
				isActive: true,
				trialDaysRemaining: 0,
				devices: [],
			};
		},
		enabled: !!licenseKey,
		staleTime: 5 * 60 * 1000, // 5 minutes
		gcTime: 10 * 60 * 1000, // 10 minutes
	});
}

// License Status Query
export function useLicenseStatus(licenseKey: string) {
	return useQuery({
		queryKey: queryKeys.licenses.status(licenseKey),
		queryFn: () => licenseApi.getStatus(licenseKey),
		enabled: !!licenseKey,
		staleTime: 1 * 60 * 1000, // 1 minute
		gcTime: 5 * 60 * 1000, // 5 minutes
	});
}

// Mutations for License Management
export function useCreateLicense() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: licenseApi.create,
		onSuccess: () => {
			// Invalidate and refetch license-related queries
			getInvalidationKeys.onLicenseChange().forEach((queryKey) => {
				queryClient.invalidateQueries({ queryKey });
			});
		},
	});
}

export function useUpgradeLicense() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: licenseApi.upgrade,
		onSuccess: (_, variables) => {
			// Invalidate specific license and related queries
			queryClient.invalidateQueries({
				queryKey: queryKeys.licenses.detail(variables.licenseKey),
			});
			queryClient.invalidateQueries({
				queryKey: queryKeys.licenses.status(variables.licenseKey),
			});

			// Invalidate list queries
			queryClient.invalidateQueries({
				queryKey: queryKeys.licenses.lists(),
			});

			// Invalidate dashboard data
			getInvalidationKeys.onLicenseChange().forEach((queryKey) => {
				queryClient.invalidateQueries({ queryKey });
			});
		},
	});
}

export function useResendLicense() {
	return useMutation({
		mutationFn: (email: string) => licenseApi.resend(email),
	});
}

export function useRemoveDevice() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ deviceId, token }: { deviceId: string; token: string }) =>
			licenseApi.removeDevice(deviceId, token),
		onSuccess: () => {
			// Invalidate license-related queries
			queryClient.invalidateQueries({
				queryKey: queryKeys.licenses.all,
			});
		},
	});
}
