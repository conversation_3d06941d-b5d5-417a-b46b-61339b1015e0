"use client";

import {
	Activity,
	BarChart3,
	CreditCard,
	Key,
	Receipt,
	Settings,
	Shield,
	Users,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarGroup,
	SidebarGroupContent,
	SidebarGroupLabel,
	SidebarHeader,
	SidebarInset,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarProvider,
	SidebarTrigger,
} from "@/components/ui/sidebar";
import UserMenu from "@/components/user-menu";
import { useUserPermissions } from "@/hooks/use-user-management";

interface DashboardLayoutProps {
	children: React.ReactNode;
}

const navigation = [
	{
		name: "Overview",
		href: "/dashboard",
		icon: Activity,
	},
	{
		name: "License Management",
		href: "/dashboard/licenses",
		icon: Key,
	},
	{
		name: "Customer Management",
		href: "/dashboard/customers",
		icon: Users,
	},
	{
		name: "Payment & Billing",
		href: "/dashboard/payments",
		icon: CreditCard,
	},
	{
		name: "Tax Compliance",
		href: "/dashboard/tax",
		icon: Receipt,
		badge: "MX",
	},
	{
		name: "Analytics & Reports",
		href: "/dashboard/analytics",
		icon: BarChart3,
	},
	{
		name: "User Management",
		href: "/dashboard/users",
		icon: Shield,
	},
	{
		name: "System Administration",
		href: "/dashboard/admin",
		icon: Settings,
	},
];

export default function DashboardLayout({ children }: DashboardLayoutProps) {
	const pathname = usePathname();
	const { canManageUsers, canInviteUsers } = useUserPermissions();

	// Filter navigation items based on permissions
	const filteredNavigation = navigation.filter((item) => {
		// Show User Management only if user has management permissions
		if (item.href === "/dashboard/users") {
			return canManageUsers || canInviteUsers;
		}
		return true;
	});

	return (
		<SidebarProvider>
			<Sidebar>
				<SidebarHeader>
					<div className="flex items-center gap-2 px-2 py-2">
						<div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
							<Activity className="size-4" />
						</div>
						<div className="grid flex-1 text-left text-sm leading-tight">
							<span className="truncate font-semibold">SnapBack</span>
							<span className="truncate text-xs">Dashboard</span>
						</div>
					</div>
				</SidebarHeader>
				<SidebarContent>
					<SidebarGroup>
						<SidebarGroupLabel>Navigation</SidebarGroupLabel>
						<SidebarGroupContent>
							<SidebarMenu>
								{filteredNavigation.map((item) => {
									const Icon = item.icon;
									const isActive =
										pathname === item.href ||
										(item.href !== "/dashboard" &&
											pathname.startsWith(item.href));
									return (
										<SidebarMenuItem key={item.name}>
											<SidebarMenuButton asChild isActive={isActive}>
												<Link
													href={item.href}
													className="flex items-center gap-2"
												>
													<Icon />
													<span>{item.name}</span>
													{item.badge && (
														<Badge
															variant="secondary"
															className="ml-auto text-xs"
														>
															{item.badge}
														</Badge>
													)}
												</Link>
											</SidebarMenuButton>
										</SidebarMenuItem>
									);
								})}
							</SidebarMenu>
						</SidebarGroupContent>
					</SidebarGroup>
				</SidebarContent>
				<SidebarFooter>
					<div className="p-2">
						<UserMenu />
					</div>
				</SidebarFooter>
			</Sidebar>
			<SidebarInset>
				<header className="sticky top-0 z-10 flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 px-4 backdrop-blur supports-[backdrop-filter]:bg-background/60">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<div className="flex flex-1 items-center justify-between">
						<div className="font-medium text-sm">
							{navigation.find((item) => item.href === pathname)?.name ||
								"Dashboard"}
						</div>
					</div>
				</header>
				<div className="flex flex-1 flex-col gap-4 bg-muted/20 p-6">
					{children}
				</div>
			</SidebarInset>
		</SidebarProvider>
	);
}
