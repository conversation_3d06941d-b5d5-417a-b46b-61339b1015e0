/**
 * Pending Invitations Management Component
 * Displays and manages user invitations with status tracking and actions
 */

"use client";

import type { InvitationStatus, UserInvitation } from "@snapback/shared";
import { format } from "date-fns";
import {
	AlertTriangle,
	Calendar,
	CheckCircle,
	Clock,
	Mail,
	MoreHorizontal,
	RefreshCw,
	Trash2,
	XCircle,
} from "lucide-react";
import { useState } from "react";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import {
	useInvitations,
	useRevokeInvitation,
	useUserPermissions,
} from "@/hooks/use-user-management";

interface InvitationsTableProps {
	className?: string;
}

const STATUS_CONFIG: Record<
	InvitationStatus,
	{
		label: string;
		variant: "default" | "secondary" | "destructive" | "outline";
		icon: React.ComponentType<{ className?: string }>;
	}
> = {
	PENDING: {
		label: "Pending",
		variant: "default",
		icon: Clock,
	},
	ACCEPTED: {
		label: "Accepted",
		variant: "secondary",
		icon: CheckCircle,
	},
	EXPIRED: {
		label: "Expired",
		variant: "destructive",
		icon: AlertTriangle,
	},
	REVOKED: {
		label: "Revoked",
		variant: "outline",
		icon: XCircle,
	},
};

export function InvitationsTable({ className }: InvitationsTableProps) {
	const [revokeInvitationId, setRevokeInvitationId] = useState<string | null>(
		null,
	);
	const { canInviteUsers } = useUserPermissions();
	const {
		data: invitationsData,
		isLoading,
		error,
		refetch,
	} = useInvitations({
		limit: 50, // Show more invitations since they're typically fewer than users
	});
	const revokeInvitation = useRevokeInvitation();

	const handleRevokeInvitation = async (invitationId: string) => {
		try {
			await revokeInvitation.mutateAsync(invitationId);
			setRevokeInvitationId(null);
		} catch (error) {
			// Error handling is done in the mutation hook
			console.error("Failed to revoke invitation:", error);
		}
	};

	// Don't render if user doesn't have permission
	if (!canInviteUsers) {
		return null;
	}

	if (error) {
		return (
			<Card className={className}>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Mail className="h-5 w-5" />
						Pending Invitations
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="py-8 text-center">
						<AlertTriangle className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
						<p className="mb-4 text-muted-foreground">
							Failed to load invitations
						</p>
						<Button onClick={() => refetch()} variant="outline">
							<RefreshCw className="mr-2 h-4 w-4" />
							Try Again
						</Button>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<>
			<Card className={className}>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Mail className="h-5 w-5" />
						Pending Invitations
					</CardTitle>
					<CardDescription>
						Manage user invitations and track their status
					</CardDescription>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-3">
							{Array.from({ length: 3 }).map((_, i) => (
								// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
								<div key={i} className="flex items-center space-x-4">
									<Skeleton className="h-4 w-[200px]" />
									<Skeleton className="h-4 w-[100px]" />
									<Skeleton className="h-4 w-[120px]" />
									<Skeleton className="h-8 w-8" />
								</div>
							))}
						</div>
					) : !invitationsData?.invitations?.length ? (
						<div className="py-8 text-center">
							<Mail className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
							<p className="mb-2 text-muted-foreground">
								No pending invitations
							</p>
							<p className="text-muted-foreground text-sm">
								Invitations you send will appear here
							</p>
						</div>
					) : (
						<div className="rounded-md border">
							<Table>
								<TableHeader>
									<TableRow>
										<TableHead>Email</TableHead>
										<TableHead>Role</TableHead>
										<TableHead>Status</TableHead>
										<TableHead>Expires</TableHead>
										<TableHead>Invited By</TableHead>
										<TableHead className="w-[50px]" />
									</TableRow>
								</TableHeader>
								<TableBody>
									{invitationsData.invitations.map((invitation) => {
										const statusConfig = STATUS_CONFIG[invitation.status];
										const StatusIcon = statusConfig.icon;
										const isExpired =
											new Date(invitation.expiresAt) < new Date();
										const canRevoke =
											invitation.status === "PENDING" && !isExpired;

										return (
											<TableRow key={invitation.id}>
												<TableCell className="font-medium">
													{invitation.email}
												</TableCell>
												<TableCell>
													<Badge variant="outline">{invitation.role}</Badge>
												</TableCell>
												<TableCell>
													<Badge
														variant={statusConfig.variant}
														className="gap-1"
													>
														<StatusIcon className="h-3 w-3" />
														{statusConfig.label}
													</Badge>
												</TableCell>
												<TableCell>
													<div className="flex items-center gap-1 text-muted-foreground text-sm">
														<Calendar className="h-3 w-3" />
														{format(
															new Date(invitation.expiresAt),
															"MMM d, yyyy",
														)}
													</div>
												</TableCell>
												<TableCell className="text-muted-foreground text-sm">
													{invitation.invitedBy?.name || "Unknown"}
												</TableCell>
												<TableCell>
													{canRevoke && (
														<DropdownMenu>
															<DropdownMenuTrigger asChild>
																<Button variant="ghost" size="sm">
																	<MoreHorizontal className="h-4 w-4" />
																</Button>
															</DropdownMenuTrigger>
															<DropdownMenuContent align="end">
																<DropdownMenuItem
																	onClick={() =>
																		setRevokeInvitationId(invitation.id)
																	}
																	className="text-destructive"
																>
																	<Trash2 className="mr-2 h-4 w-4" />
																	Revoke Invitation
																</DropdownMenuItem>
															</DropdownMenuContent>
														</DropdownMenu>
													)}
												</TableCell>
											</TableRow>
										);
									})}
								</TableBody>
							</Table>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Revoke Confirmation Dialog */}
			<AlertDialog
				open={!!revokeInvitationId}
				onOpenChange={() => setRevokeInvitationId(null)}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Revoke Invitation</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to revoke this invitation? The recipient
							will no longer be able to use the invitation link to join the
							system.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={() =>
								revokeInvitationId && handleRevokeInvitation(revokeInvitationId)
							}
							disabled={revokeInvitation.isPending}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							{revokeInvitation.isPending ? "Revoking..." : "Revoke Invitation"}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
