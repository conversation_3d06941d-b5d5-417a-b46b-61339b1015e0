/**
 * User Management Table Component
 * Displays and manages users with role-based actions and status controls
 */

"use client";

import type { UserR<PERSON>, User as UserType } from "@snapback/shared";
import { format } from "date-fns";
import {
	AlertTriangle,
	CheckCircle,
	Crown,
	Edit,
	MoreHorizontal,
	RefreshCw,
	Shield,
	ShieldCheck,
	User,
	UserCheck,
	Users,
	UserX,
	XCircle,
} from "lucide-react";
import { useState } from "react";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import {
	useToggleUserStatus,
	useUserPermissions,
	useUsers,
} from "@/hooks/use-user-management";
import { InvitationForm } from "./invitation-form";

interface UsersTableProps {
	className?: string;
}

const ROLE_CONFIG: Record<
	UserRole,
	{
		label: string;
		variant: "default" | "secondary" | "destructive" | "outline";
		icon: React.ComponentType<{ className?: string }>;
	}
> = {
	SUPER_ADMIN: {
		label: "Super Admin",
		variant: "destructive",
		icon: Crown,
	},
	ADMIN: {
		label: "Admin",
		variant: "default",
		icon: ShieldCheck,
	},
	MANAGER: {
		label: "Manager",
		variant: "secondary",
		icon: Shield,
	},
	USER: {
		label: "User",
		variant: "outline",
		icon: User,
	},
	VIEWER: {
		label: "Viewer",
		variant: "outline",
		icon: UserCheck,
	},
};

export function UsersTable({ className }: UsersTableProps) {
	const [search, setSearch] = useState("");
	const [roleFilter, setRoleFilter] = useState<string>("all");
	const [statusFilter, setStatusFilter] = useState<string>("all");
	const [toggleUserId, setToggleUserId] = useState<string | null>(null);
	const [toggleAction, setToggleAction] = useState<
		"activate" | "deactivate" | null
	>(null);

	const { canManageUsers, canDeactivateUsers, assignableRoles } =
		useUserPermissions();

	const {
		data: usersData,
		isLoading,
		error,
		refetch,
	} = useUsers({
		page: 1,
		search: search || undefined,
		role: roleFilter === "all" ? undefined : (roleFilter as UserRole),
		isActive: statusFilter === "all" ? undefined : statusFilter === "active",
		limit: 50,
	});

	const toggleUserStatus = useToggleUserStatus();

	const handleToggleUserStatus = async () => {
		if (!toggleUserId || !toggleAction) return;

		try {
			await toggleUserStatus.mutateAsync({
				userId: toggleUserId,
				isActive: toggleAction === "activate",
			});
			setToggleUserId(null);
			setToggleAction(null);
		} catch (error) {
			// Error handling is done in the mutation hook
			console.error("Failed to toggle user status:", error);
		}
	};

	// Don't render if user doesn't have permission
	if (!canManageUsers) {
		return null;
	}

	if (error) {
		return (
			<Card className={className}>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Users className="h-5 w-5" />
						User Management
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="py-8 text-center">
						<AlertTriangle className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
						<p className="mb-4 text-muted-foreground">Failed to load users</p>
						<Button onClick={() => refetch()} variant="outline">
							<RefreshCw className="mr-2 h-4 w-4" />
							Try Again
						</Button>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<>
			<Card className={className}>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle className="flex items-center gap-2">
								<Users className="h-5 w-5" />
								User Management
							</CardTitle>
							<CardDescription>
								Manage user accounts, roles, and permissions
							</CardDescription>
						</div>
						<InvitationForm />
					</div>
				</CardHeader>
				<CardContent>
					{/* Filters */}
					<div className="mb-6 flex flex-col gap-4 sm:flex-row">
						<div className="flex-1">
							<Input
								placeholder="Search users by name or email..."
								value={search}
								onChange={(e) => setSearch(e.target.value)}
								className="max-w-sm"
							/>
						</div>
						<div className="flex gap-2">
							<Select value={roleFilter} onValueChange={setRoleFilter}>
								<SelectTrigger className="w-[140px]">
									<SelectValue placeholder="All Roles" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Roles</SelectItem>
									{assignableRoles.map((role) => (
										<SelectItem key={role} value={role}>
											{ROLE_CONFIG[role].label}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
							<Select value={statusFilter} onValueChange={setStatusFilter}>
								<SelectTrigger className="w-[140px]">
									<SelectValue placeholder="All Status" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Status</SelectItem>
									<SelectItem value="active">Active</SelectItem>
									<SelectItem value="inactive">Inactive</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>

					{isLoading ? (
						<div className="space-y-3">
							{Array.from({ length: 5 }).map((_, i) => (
								// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
								<div key={i} className="flex items-center space-x-4">
									<Skeleton className="h-4 w-[200px]" />
									<Skeleton className="h-4 w-[100px]" />
									<Skeleton className="h-4 w-[80px]" />
									<Skeleton className="h-4 w-[120px]" />
									<Skeleton className="h-8 w-8" />
								</div>
							))}
						</div>
					) : !usersData?.users?.length ? (
						<div className="py-8 text-center">
							<Users className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
							<p className="mb-2 text-muted-foreground">No users found</p>
							<p className="text-muted-foreground text-sm">
								Try adjusting your search or filters
							</p>
						</div>
					) : (
						<div className="rounded-md border">
							<Table>
								<TableHeader>
									<TableRow>
										<TableHead>User</TableHead>
										<TableHead>Role</TableHead>
										<TableHead>Status</TableHead>
										<TableHead>Joined</TableHead>
										<TableHead>Last Login</TableHead>
										<TableHead className="w-[50px]" />
									</TableRow>
								</TableHeader>
								<TableBody>
									{usersData.users.map((user) => {
										const roleConfig = ROLE_CONFIG[user.role as UserRole];
										const RoleIcon = roleConfig.icon;

										return (
											<TableRow key={user.id}>
												<TableCell>
													<div className="flex items-center gap-3">
														<div className="flex-shrink-0">
															{user.image ? (
																<img
																	src={user.image}
																	alt={user.name}
																	className="h-8 w-8 rounded-full"
																/>
															) : (
																<div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
																	<User className="h-4 w-4" />
																</div>
															)}
														</div>
														<div>
															<div className="font-medium">{user.name}</div>
															<div className="text-muted-foreground text-sm">
																{user.email}
															</div>
														</div>
													</div>
												</TableCell>
												<TableCell>
													<Badge variant={roleConfig.variant} className="gap-1">
														<RoleIcon className="h-3 w-3" />
														{roleConfig.label}
													</Badge>
												</TableCell>
												<TableCell>
													<Badge
														variant={user.isActive ? "secondary" : "outline"}
														className="gap-1"
													>
														{user.isActive ? (
															<>
																<CheckCircle className="h-3 w-3" />
																Active
															</>
														) : (
															<>
																<XCircle className="h-3 w-3" />
																Inactive
															</>
														)}
													</Badge>
												</TableCell>
												<TableCell className="text-muted-foreground text-sm">
													{format(new Date(user.createdAt), "MMM d, yyyy")}
												</TableCell>
												<TableCell className="text-muted-foreground text-sm">
													{user.lastLoginAt
														? format(new Date(user.lastLoginAt), "MMM d, yyyy")
														: "Never"}
												</TableCell>
												<TableCell>
													<DropdownMenu>
														<DropdownMenuTrigger asChild>
															<Button variant="ghost" size="sm">
																<MoreHorizontal className="h-4 w-4" />
															</Button>
														</DropdownMenuTrigger>
														<DropdownMenuContent align="end">
															<DropdownMenuItem>
																<Edit className="mr-2 h-4 w-4" />
																Edit User
															</DropdownMenuItem>
															{canDeactivateUsers && (
																<>
																	<DropdownMenuSeparator />
																	{user.isActive ? (
																		<DropdownMenuItem
																			onClick={() => {
																				setToggleUserId(user.id);
																				setToggleAction("deactivate");
																			}}
																			className="text-destructive"
																		>
																			<UserX className="mr-2 h-4 w-4" />
																			Deactivate User
																		</DropdownMenuItem>
																	) : (
																		<DropdownMenuItem
																			onClick={() => {
																				setToggleUserId(user.id);
																				setToggleAction("activate");
																			}}
																		>
																			<UserCheck className="mr-2 h-4 w-4" />
																			Activate User
																		</DropdownMenuItem>
																	)}
																</>
															)}
														</DropdownMenuContent>
													</DropdownMenu>
												</TableCell>
											</TableRow>
										);
									})}
								</TableBody>
							</Table>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Status Toggle Confirmation Dialog */}
			<AlertDialog
				open={!!toggleUserId}
				onOpenChange={() => {
					setToggleUserId(null);
					setToggleAction(null);
				}}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>
							{toggleAction === "activate" ? "Activate" : "Deactivate"} User
						</AlertDialogTitle>
						<AlertDialogDescription>
							{toggleAction === "activate"
								? "Are you sure you want to activate this user? They will regain access to the system."
								: "Are you sure you want to deactivate this user? They will lose access to the system."}
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={handleToggleUserStatus}
							disabled={toggleUserStatus.isPending}
							className={
								toggleAction === "deactivate"
									? "bg-destructive text-destructive-foreground hover:bg-destructive/90"
									: ""
							}
						>
							{toggleUserStatus.isPending
								? `${toggleAction === "activate" ? "Activating" : "Deactivating"}...`
								: `${toggleAction === "activate" ? "Activate" : "Deactivate"} User`}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
