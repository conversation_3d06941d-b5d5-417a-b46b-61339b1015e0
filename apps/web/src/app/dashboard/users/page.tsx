/**
 * User Management Page
 * Admin page for managing users and invitations with RBAC
 */

import type { Metadata } from "next";
import {
	DebugPermissions,
	UserManagementDashboard,
} from "@/components/user-management";

export const metadata: Metadata = {
	title: "User Management | SnapBack Admin",
	description: "Manage user accounts, roles, and invitations",
};

export default function UserManagementPage() {
	return (
		<div className="container mx-auto py-8">
			<div className="mb-8">
				<h1 className="font-bold text-3xl tracking-tight">User Management</h1>
				<p className="mt-2 text-muted-foreground">
					Manage user accounts, assign roles, and send invitations to new team
					members.
				</p>
			</div>

			<DebugPermissions />
			<div className="mt-8">
				<UserManagementDashboard />
			</div>
		</div>
	);
}
