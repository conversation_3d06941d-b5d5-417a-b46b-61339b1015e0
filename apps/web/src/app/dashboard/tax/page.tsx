"use client";

import { Calendar, Download, FileText, Filter, Search } from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";

interface TaxReport {
	id: string;
	month: string;
	year: number;
	totalRevenue: number;
	ivaCollected: number;
	transactionCount: number;
	customersWithRfc: number;
	status: "draft" | "submitted" | "approved";
	createdAt: Date;
	submittedAt?: Date;
}

// Mock tax report data
const mockTaxReports: TaxReport[] = [
	{
		id: "1",
		month: "January",
		year: 2024,
		totalRevenue: 2495,
		ivaCollected: 399,
		transactionCount: 5,
		customersWithRfc: 3,
		status: "approved",
		createdAt: new Date("2024-02-01T10:00:00Z"),
		submittedAt: new Date("2024-02-05T14:30:00Z"),
	},
	{
		id: "2",
		month: "February",
		year: 2024,
		totalRevenue: 1998,
		ivaCollected: 320,
		transactionCount: 4,
		customersWithRfc: 2,
		status: "submitted",
		createdAt: new Date("2024-03-01T10:00:00Z"),
		submittedAt: new Date("2024-03-03T16:15:00Z"),
	},
	{
		id: "3",
		month: "March",
		year: 2024,
		totalRevenue: 3492,
		ivaCollected: 559,
		transactionCount: 7,
		customersWithRfc: 5,
		status: "draft",
		createdAt: new Date("2024-04-01T10:00:00Z"),
	},
];

export default function TaxCompliancePage() {
	const [searchTerm, setSearchTerm] = useState("");
	const [statusFilter, setStatusFilter] = useState<string>("all");
	const [yearFilter, setYearFilter] = useState<string>("2024");

	// Filter reports
	const filteredReports = mockTaxReports.filter((report) => {
		const matchesSearch = report.month
			.toLowerCase()
			.includes(searchTerm.toLowerCase());
		const matchesStatus =
			statusFilter === "all" || report.status === statusFilter;
		const matchesYear =
			yearFilter === "all" || report.year.toString() === yearFilter;

		return matchesSearch && matchesStatus && matchesYear;
	});

	const formatCurrency = (amount: number, currency = "MXN") => {
		return new Intl.NumberFormat("es-MX", {
			style: "currency",
			currency,
		}).format(amount / 100);
	};

	const formatDate = (date: Date) => {
		return new Intl.DateTimeFormat("es-MX", {
			year: "numeric",
			month: "short",
			day: "numeric",
		}).format(date);
	};

	const getStatusBadge = (status: TaxReport["status"]) => {
		switch (status) {
			case "approved":
				return <Badge variant="default">Approved</Badge>;
			case "submitted":
				return <Badge variant="secondary">Submitted</Badge>;
			case "draft":
				return <Badge variant="outline">Draft</Badge>;
			default:
				return <Badge variant="outline">Unknown</Badge>;
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
				<div>
					<h1 className="font-bold text-3xl tracking-tight">
						Tax Compliance (Mexico)
					</h1>
					<p className="text-muted-foreground">
						SAT-compliant reporting and tax management
					</p>
				</div>
				<div className="flex gap-2">
					<Button>
						<FileText className="mr-2 h-4 w-4" />
						Generate Report
					</Button>
					<Button variant="outline">
						<Download className="mr-2 h-4 w-4" />
						Export SAT XML
					</Button>
				</div>
			</div>

			{/* Filters */}
			<div className="flex flex-col gap-4 sm:flex-row">
				<div className="relative flex-1">
					<Search className="absolute top-2.5 left-2 h-4 w-4 text-muted-foreground" />
					<Input
						placeholder="Search by month..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="pl-8"
					/>
				</div>
				<Select value={yearFilter} onValueChange={setYearFilter}>
					<SelectTrigger className="w-[120px]">
						<Calendar className="mr-2 h-4 w-4" />
						<SelectValue placeholder="Year" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">All Years</SelectItem>
						<SelectItem value="2024">2024</SelectItem>
						<SelectItem value="2023">2023</SelectItem>
					</SelectContent>
				</Select>
				<Select value={statusFilter} onValueChange={setStatusFilter}>
					<SelectTrigger className="w-[140px]">
						<Filter className="mr-2 h-4 w-4" />
						<SelectValue placeholder="Status" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">All Status</SelectItem>
						<SelectItem value="draft">Draft</SelectItem>
						<SelectItem value="submitted">Submitted</SelectItem>
						<SelectItem value="approved">Approved</SelectItem>
					</SelectContent>
				</Select>
				<Button variant="outline">
					<Download className="mr-2 h-4 w-4" />
					Export
				</Button>
			</div>

			{/* Tax Reports Table */}
			<Card>
				<CardHeader>
					<CardTitle>Monthly Tax Reports ({filteredReports.length})</CardTitle>
					<CardDescription>
						SAT-compliant monthly tax reports and submissions
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="overflow-x-auto">
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Period</TableHead>
									<TableHead>Revenue (MXN)</TableHead>
									<TableHead>IVA Collected</TableHead>
									<TableHead>Transactions</TableHead>
									<TableHead>RFC Compliance</TableHead>
									<TableHead>Status</TableHead>
									<TableHead>Submitted</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{filteredReports.map((report) => (
									<TableRow key={report.id}>
										<TableCell>
											<div className="font-medium">
												{report.month} {report.year}
											</div>
										</TableCell>
										<TableCell>
											<div className="font-medium">
												{formatCurrency(report.totalRevenue)}
											</div>
										</TableCell>
										<TableCell>
											<div className="font-medium">
												{formatCurrency(report.ivaCollected)}
											</div>
										</TableCell>
										<TableCell>{report.transactionCount}</TableCell>
										<TableCell>
											<div className="flex items-center gap-2">
												<span>
													{report.customersWithRfc}/{report.transactionCount}
												</span>
												<Badge variant="outline" className="text-xs">
													{Math.round(
														(report.customersWithRfc /
															report.transactionCount) *
															100,
													)}
													%
												</Badge>
											</div>
										</TableCell>
										<TableCell>{getStatusBadge(report.status)}</TableCell>
										<TableCell>
											{report.submittedAt
												? formatDate(report.submittedAt)
												: "-"}
										</TableCell>
									</TableRow>
								))}
							</TableBody>
						</Table>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
