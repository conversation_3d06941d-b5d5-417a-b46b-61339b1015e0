"use client";

import {
	Activity,
	CreditCard,
	DollarSign,
	Key,
	Shield,
	TrendingUp,
	Users,
} from "lucide-react";
import { useState } from "react";
import {
	Area,
	AreaChart,
	Cell,
	Pie,
	PieChart,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from "recharts";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	mockActivityItems,
	mockDashboardStats,
	mockLicenseAnalytics,
	mockRevenueData,
} from "@/lib/mock-data";
import type { ActivityItem, Currency } from "@/types/dashboard";

interface OverviewDashboardProps {
	className?: string;
}

export default function OverviewDashboard({
	className,
}: OverviewDashboardProps) {
	const [currency, setCurrency] = useState<Currency>("USD");
	const [timeRange, setTimeRange] = useState("7d");

	// Mock data - in real implementation, this would come from API
	const stats = mockDashboardStats;
	const revenueData = mockRevenueData;
	const activityItems = mockActivityItems;
	const licenseAnalytics = mockLicenseAnalytics;

	const formatCurrency = (amount: number, curr: Currency = currency) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: curr,
		}).format(curr === "MXN" ? amount * 20 : amount); // Mock conversion rate
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleString();
	};

	const getActivityIcon = (type: ActivityItem["type"]) => {
		switch (type) {
			case "license_created":
				return <Key className="h-4 w-4 text-blue-600" />;
			case "payment_received":
				return <CreditCard className="h-4 w-4 text-green-600" />;
			case "refund_requested":
				return <DollarSign className="h-4 w-4 text-orange-600" />;
			case "device_added":
				return <Shield className="h-4 w-4 text-purple-600" />;
			case "license_upgraded":
				return <Key className="h-4 w-4 text-indigo-600" />;
			default:
				return <Activity className="h-4 w-4 text-gray-600" />;
		}
	};

	// Chart data
	const licenseDistributionData = [
		{
			name: "Standard",
			value: licenseAnalytics.standardLicenses,
			color: "#3b82f6",
		},
		{
			name: "Extended",
			value: licenseAnalytics.extendedLicenses,
			color: "#10b981",
		},
		{ name: "Trial", value: licenseAnalytics.trialLicenses, color: "#f59e0b" },
	];

	return (
		<div className={`space-y-6 ${className}`}>
			{/* Header with Controls */}
			<div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
				<div>
					<h1 className="font-bold text-3xl tracking-tight">
						Dashboard Overview
					</h1>
					<p className="text-muted-foreground">
						Welcome to your SnapBack license management dashboard
					</p>
				</div>
				<div className="flex gap-2">
					<Select
						value={currency}
						onValueChange={(value: Currency) => setCurrency(value)}
					>
						<SelectTrigger className="w-[100px]">
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="USD">USD</SelectItem>
							<SelectItem value="MXN">MXN</SelectItem>
						</SelectContent>
					</Select>
					<Select value={timeRange} onValueChange={setTimeRange}>
						<SelectTrigger className="w-[120px]">
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="7d">Last 7 days</SelectItem>
							<SelectItem value="30d">Last 30 days</SelectItem>
							<SelectItem value="90d">Last 90 days</SelectItem>
							<SelectItem value="1y">Last year</SelectItem>
						</SelectContent>
					</Select>
				</div>
			</div>

			{/* Stats Grid */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="font-medium text-sm">Total Revenue</CardTitle>
						<DollarSign className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="font-bold text-2xl">
							{formatCurrency(stats.totalRevenue)}
						</div>
						<p className="text-muted-foreground text-xs">
							<span className="font-medium text-green-600">
								+{stats.monthlyGrowthRate}%
							</span>{" "}
							from last month
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="font-medium text-sm">
							Active Licenses
						</CardTitle>
						<Key className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="font-bold text-2xl">
							{stats.activeLicenses.toLocaleString()}
						</div>
						<p className="text-muted-foreground text-xs">
							{stats.totalLicenses.toLocaleString()} total licenses
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="font-medium text-sm">
							Total Customers
						</CardTitle>
						<Users className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="font-bold text-2xl">
							{stats.totalCustomers.toLocaleString()}
						</div>
						<p className="text-muted-foreground text-xs">
							Active customer base
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="font-medium text-sm">System Health</CardTitle>
						<Shield className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="font-bold text-2xl">
							<Badge
								variant={
									stats.systemHealth === "healthy"
										? "default"
										: stats.systemHealth === "warning"
											? "secondary"
											: "destructive"
								}
								className="text-sm"
							>
								{stats.systemHealth}
							</Badge>
						</div>
						<p className="text-muted-foreground text-xs">
							{stats.pendingRefunds} pending refunds
						</p>
					</CardContent>
				</Card>
			</div>

			{/* Charts Section */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
				{/* Revenue Trend Chart */}
				<Card className="col-span-4">
					<CardHeader>
						<CardTitle>Revenue Trend</CardTitle>
						<CardDescription>
							Daily revenue over the selected period
						</CardDescription>
					</CardHeader>
					<CardContent>
						<ResponsiveContainer width="100%" height={300}>
							<AreaChart data={revenueData}>
								<defs>
									<linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
										<stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
										<stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1} />
									</linearGradient>
								</defs>
								<XAxis
									dataKey="date"
									tickFormatter={(value) =>
										new Date(value).toLocaleDateString()
									}
								/>
								<YAxis tickFormatter={(value) => formatCurrency(value)} />
								<Tooltip
									formatter={(value: number) => [
										formatCurrency(value),
										"Revenue",
									]}
									labelFormatter={(label) =>
										new Date(label).toLocaleDateString()
									}
								/>
								<Area
									type="monotone"
									dataKey="revenue"
									stroke="#3b82f6"
									fillOpacity={1}
									fill="url(#colorRevenue)"
								/>
							</AreaChart>
						</ResponsiveContainer>
					</CardContent>
				</Card>

				{/* License Distribution Chart */}
				<Card className="col-span-3">
					<CardHeader>
						<CardTitle>License Distribution</CardTitle>
						<CardDescription>Breakdown by license type</CardDescription>
					</CardHeader>
					<CardContent>
						<ResponsiveContainer width="100%" height={300}>
							<PieChart>
								<Pie
									data={licenseDistributionData}
									cx="50%"
									cy="50%"
									labelLine={false}
									label={({ name, percent }) =>
										`${name} ${(percent * 100).toFixed(0)}%`
									}
									outerRadius={80}
									fill="#8884d8"
									dataKey="value"
								>
									{licenseDistributionData.map((entry, _index) => (
										<Cell key={`cell-${entry.name}`} fill={entry.color} />
									))}
								</Pie>
								<Tooltip />
							</PieChart>
						</ResponsiveContainer>
					</CardContent>
				</Card>
			</div>

			{/* Recent Activity */}
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<CardTitle>Recent Activity</CardTitle>
						<Button variant="outline" size="sm">
							View All
						</Button>
					</div>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{activityItems.slice(0, 5).map((activity) => (
							<div key={activity.id} className="flex items-center space-x-4">
								<div className="flex-shrink-0">
									{getActivityIcon(activity.type)}
								</div>
								<div className="flex-1 space-y-1">
									<p className="font-medium text-sm leading-none">
										{activity.description}
									</p>
									<div className="flex items-center space-x-2">
										<p className="text-muted-foreground text-xs">
											{formatDate(activity.timestamp.toISOString())}
										</p>
										{activity.amount && (
											<span className="font-medium text-green-600 text-xs">
												{formatCurrency(activity.amount, activity.currency)}
											</span>
										)}
									</div>
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Quick Actions */}
			<Card>
				<CardHeader>
					<CardTitle>Quick Actions</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
						<Button className="flex items-center justify-center space-x-2">
							<Key className="h-4 w-4" />
							<span>Create License</span>
						</Button>
						<Button
							variant="outline"
							className="flex items-center justify-center space-x-2"
						>
							<Users className="h-4 w-4" />
							<span>View Customers</span>
						</Button>
						<Button
							variant="outline"
							className="flex items-center justify-center space-x-2"
						>
							<TrendingUp className="h-4 w-4" />
							<span>View Analytics</span>
						</Button>
						<Button
							variant="outline"
							className="flex items-center justify-center space-x-2"
						>
							<Activity className="h-4 w-4" />
							<span>System Status</span>
						</Button>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
