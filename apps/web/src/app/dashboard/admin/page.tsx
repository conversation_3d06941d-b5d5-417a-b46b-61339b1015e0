"use client";

import {
	<PERSON>,
	Download,
	HardDrive,
	<PERSON>f<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON>,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";

interface SystemMetric {
	name: string;
	value: string;
	status: "healthy" | "warning" | "critical";
	lastUpdated: Date;
}

interface ActivityLog {
	id: string;
	action: string;
	user: string;
	timestamp: Date;
	details: string;
	type: "info" | "warning" | "error";
}

// Mock system metrics
const systemMetrics: SystemMetric[] = [
	{
		name: "Database Connections",
		value: "45/100",
		status: "healthy",
		lastUpdated: new Date(),
	},
	{
		name: "Memory Usage",
		value: "68%",
		status: "warning",
		lastUpdated: new Date(),
	},
	{
		name: "Disk Space",
		value: "23.4 GB / 100 GB",
		status: "healthy",
		lastUpdated: new Date(),
	},
	{
		name: "API Response Time",
		value: "145ms",
		status: "healthy",
		lastUpdated: new Date(),
	},
];

// Mock activity logs
const activityLogs: ActivityLog[] = [
	{
		id: "1",
		action: "License Created",
		user: "<EMAIL>",
		timestamp: new Date("2024-01-15T10:30:00Z"),
		details: "Created <NAME_EMAIL>",
		type: "info",
	},
	{
		id: "2",
		action: "Payment Processed",
		user: "system",
		timestamp: new Date("2024-01-15T10:25:00Z"),
		details: "Payment of $4.99 processed successfully",
		type: "info",
	},
	{
		id: "3",
		action: "Failed Login Attempt",
		user: "unknown",
		timestamp: new Date("2024-01-15T10:20:00Z"),
		details: "Failed login from IP *************",
		type: "warning",
	},
	{
		id: "4",
		action: "Database Backup",
		user: "system",
		timestamp: new Date("2024-01-15T06:00:00Z"),
		details: "Daily database backup completed",
		type: "info",
	},
];

export default function SystemAdminPage() {
	const [isRefreshing, setIsRefreshing] = useState(false);

	const handleRefresh = async () => {
		setIsRefreshing(true);
		// Simulate API call
		await new Promise((resolve) => setTimeout(resolve, 2000));
		setIsRefreshing(false);
	};

	const formatDate = (date: Date) => {
		return new Intl.DateTimeFormat("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		}).format(date);
	};

	const getStatusBadge = (status: SystemMetric["status"]) => {
		switch (status) {
			case "healthy":
				return <Badge variant="default">Healthy</Badge>;
			case "warning":
				return <Badge variant="secondary">Warning</Badge>;
			case "critical":
				return <Badge variant="destructive">Critical</Badge>;
			default:
				return <Badge variant="outline">Unknown</Badge>;
		}
	};

	const getLogTypeBadge = (type: ActivityLog["type"]) => {
		switch (type) {
			case "info":
				return <Badge variant="outline">Info</Badge>;
			case "warning":
				return <Badge variant="secondary">Warning</Badge>;
			case "error":
				return <Badge variant="destructive">Error</Badge>;
			default:
				return <Badge variant="outline">Unknown</Badge>;
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
				<div>
					<h1 className="font-bold text-3xl tracking-tight">
						System Administration
					</h1>
					<p className="text-muted-foreground">
						Monitor system health and manage administrative tasks
					</p>
				</div>
				<div className="flex gap-2">
					<Button onClick={handleRefresh} disabled={isRefreshing}>
						<RefreshCw
							className={`mr-2 h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
						/>
						{isRefreshing ? "Refreshing..." : "Refresh"}
					</Button>
					<Button variant="outline">
						<Download className="mr-2 h-4 w-4" />
						Export Logs
					</Button>
				</div>
			</div>

			{/* System Metrics */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Shield className="h-5 w-5" />
						System Health Metrics
					</CardTitle>
					<CardDescription>
						Real-time system performance and health indicators
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{systemMetrics.map((metric, _index) => (
							<div
								key={metric.name}
								className="flex items-center justify-between rounded-lg border p-4"
							>
								<div className="flex items-center gap-4">
									<div>
										<p className="font-medium">{metric.name}</p>
										<p className="text-muted-foreground text-sm">
											Last updated: {formatDate(metric.lastUpdated)}
										</p>
									</div>
								</div>
								<div className="flex items-center gap-4">
									<div className="text-right">
										<p className="font-mono text-lg">{metric.value}</p>
									</div>
									{getStatusBadge(metric.status)}
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Resource Usage and Quick Actions */}
			<div className="grid gap-4 md:grid-cols-2">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<HardDrive className="h-5 w-5" />
							Storage Usage
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div>
							<div className="mb-2 flex justify-between text-sm">
								<span>Database</span>
								<span>2.4 GB / 10 GB</span>
							</div>
							<Progress value={24} className="h-2" />
						</div>
						<div>
							<div className="mb-2 flex justify-between text-sm">
								<span>File Storage</span>
								<span>1.2 GB / 5 GB</span>
							</div>
							<Progress value={24} className="h-2" />
						</div>
						<div>
							<div className="mb-2 flex justify-between text-sm">
								<span>Backups</span>
								<span>8.7 GB / 20 GB</span>
							</div>
							<Progress value={43} className="h-2" />
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Settings className="h-5 w-5" />
							Quick Actions
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-3">
						<Button className="w-full justify-start" variant="outline">
							<Database className="mr-2 h-4 w-4" />
							Run Database Backup
						</Button>
						<Button className="w-full justify-start" variant="outline">
							<RefreshCw className="mr-2 h-4 w-4" />
							Clear Application Cache
						</Button>
						<Button className="w-full justify-start" variant="outline">
							<Download className="mr-2 h-4 w-4" />
							Export System Logs
						</Button>
						<Button className="w-full justify-start" variant="outline">
							<Shield className="mr-2 h-4 w-4" />
							Run Security Scan
						</Button>
					</CardContent>
				</Card>
			</div>

			{/* Activity Logs */}
			<Card>
				<CardHeader>
					<CardTitle>Recent Activity</CardTitle>
					<CardDescription>System and user activity logs</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="overflow-x-auto">
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Action</TableHead>
									<TableHead>User</TableHead>
									<TableHead>Details</TableHead>
									<TableHead>Type</TableHead>
									<TableHead>Timestamp</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{activityLogs.map((log) => (
									<TableRow key={log.id}>
										<TableCell className="font-medium">{log.action}</TableCell>
										<TableCell>{log.user}</TableCell>
										<TableCell className="max-w-xs truncate">
											{log.details}
										</TableCell>
										<TableCell>{getLogTypeBadge(log.type)}</TableCell>
										<TableCell>{formatDate(log.timestamp)}</TableCell>
									</TableRow>
								))}
							</TableBody>
						</Table>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
