"use client";

import {
	Download,
	Edit,
	Eye,
	Filter,
	Mail,
	MapPin,
	MoreHorizontal,
	Plus,
	Receipt,
	Search,
	User,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { getMockCustomers } from "@/lib/mock-data";
import type { Customer, CustomerFilters } from "@/types/dashboard";

export default function CustomersPage() {
	const [searchTerm, setSearchTerm] = useState("");
	const [countryFilter, setCountryFilter] = useState<string>("all");
	const [taxStatusFilter, setTaxStatusFilter] = useState<string>("all");
	const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
		null,
	);
	const [isDetailsOpen, setIsDetailsOpen] = useState(false);
	const [isCreateCustomerOpen, setIsCreateCustomerOpen] = useState(false);

	// Mock data with filters
	const filters: CustomerFilters = {
		search: searchTerm,
		country: countryFilter === "all" ? undefined : countryFilter,
		taxStatus:
			taxStatusFilter === "all"
				? undefined
				: (taxStatusFilter as "rfc_provided" | "rfc_missing"),
	};

	const customers = getMockCustomers(filters);

	const formatDate = (date: Date) => {
		return new Intl.DateTimeFormat("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
		}).format(date);
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
		}).format(amount / 100);
	};

	const getCountryFlag = (countryCode: string) => {
		const flags: Record<string, string> = {
			MX: "🇲🇽",
			US: "🇺🇸",
			CA: "🇨🇦",
			ES: "🇪🇸",
		};
		return flags[countryCode] || "🌍";
	};

	const getCountryName = (countryCode: string) => {
		const countries: Record<string, string> = {
			MX: "Mexico",
			US: "United States",
			CA: "Canada",
			ES: "Spain",
		};
		return countries[countryCode] || countryCode;
	};

	const getRfcStatus = (customer: Customer) => {
		if (customer.countryCode === "MX") {
			return customer.rfc ? "rfc_provided" : "rfc_missing";
		}
		return "not_applicable";
	};

	const getRfcBadge = (customer: Customer) => {
		const status = getRfcStatus(customer);
		switch (status) {
			case "rfc_provided":
				return <Badge variant="default">RFC Provided</Badge>;
			case "rfc_missing":
				return <Badge variant="destructive">RFC Missing</Badge>;
			case "not_applicable":
				return <Badge variant="secondary">N/A</Badge>;
			default:
				return <Badge variant="outline">Unknown</Badge>;
		}
	};

	const openCustomerDetails = (customer: Customer) => {
		setSelectedCustomer(customer);
		setIsDetailsOpen(true);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
				<div>
					<h1 className="font-bold text-3xl tracking-tight">
						Customer Management
					</h1>
					<p className="text-muted-foreground">
						Manage customer information and tax compliance
					</p>
				</div>
				<Dialog
					open={isCreateCustomerOpen}
					onOpenChange={setIsCreateCustomerOpen}
				>
					<DialogTrigger asChild>
						<Button>
							<Plus className="mr-2 h-4 w-4" />
							Add Customer
						</Button>
					</DialogTrigger>
					<DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]">
						<DialogHeader>
							<DialogTitle>Add New Customer</DialogTitle>
							<DialogDescription>
								Add a new customer to the system with tax information.
							</DialogDescription>
						</DialogHeader>
						<div className="grid gap-4 py-4">
							{/* Mobile-first form layout */}
							<div className="grid grid-cols-1 items-start gap-2 sm:grid-cols-4 sm:items-center sm:gap-4">
								<Label
									htmlFor="customerName"
									className="font-medium sm:text-right"
								>
									Name
								</Label>
								<Input
									id="customerName"
									placeholder="Customer Name"
									className="sm:col-span-3"
								/>
							</div>
							<div className="grid grid-cols-1 items-start gap-2 sm:grid-cols-4 sm:items-center sm:gap-4">
								<Label
									htmlFor="customerEmail"
									className="font-medium sm:text-right"
								>
									Email
								</Label>
								<Input
									id="customerEmail"
									type="email"
									placeholder="<EMAIL>"
									className="sm:col-span-3"
								/>
							</div>
							<div className="grid grid-cols-1 items-start gap-2 sm:grid-cols-4 sm:items-center sm:gap-4">
								<Label htmlFor="country" className="font-medium sm:text-right">
									Country
								</Label>
								<Select>
									<SelectTrigger className="sm:col-span-3">
										<SelectValue placeholder="Select country" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="MX">🇲🇽 Mexico</SelectItem>
										<SelectItem value="US">🇺🇸 United States</SelectItem>
										<SelectItem value="CA">🇨🇦 Canada</SelectItem>
										<SelectItem value="ES">🇪🇸 Spain</SelectItem>
									</SelectContent>
								</Select>
							</div>
							<div className="grid grid-cols-1 items-start gap-2 sm:grid-cols-4 sm:items-center sm:gap-4">
								<Label
									htmlFor="customerRfc"
									className="font-medium sm:text-right"
								>
									RFC (Mexico)
								</Label>
								<Input
									id="customerRfc"
									placeholder="ABCD123456EFG (optional)"
									className="sm:col-span-3"
								/>
							</div>
							<div className="grid grid-cols-1 items-start gap-2 sm:grid-cols-4 sm:items-center sm:gap-4">
								<Label htmlFor="phone" className="font-medium sm:text-right">
									Phone
								</Label>
								<Input
									id="phone"
									placeholder="+52 55 1234 5678"
									className="sm:col-span-3"
								/>
							</div>
						</div>
						<div className="flex justify-end space-x-2">
							<Button
								variant="outline"
								onClick={() => setIsCreateCustomerOpen(false)}
							>
								Cancel
							</Button>
							<Button onClick={() => setIsCreateCustomerOpen(false)}>
								Add Customer
							</Button>
						</div>
					</DialogContent>
				</Dialog>
			</div>

			{/* Filters and Search */}
			<div className="flex flex-col gap-4 sm:flex-row">
				<div className="relative flex-1">
					<Search className="absolute top-2.5 left-2 h-4 w-4 text-muted-foreground" />
					<Input
						placeholder="Search customers..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="pl-8"
					/>
				</div>
				<Select value={countryFilter} onValueChange={setCountryFilter}>
					<SelectTrigger className="w-[180px]">
						<MapPin className="mr-2 h-4 w-4" />
						<SelectValue placeholder="Country" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">All Countries</SelectItem>
						<SelectItem value="MX">🇲🇽 Mexico</SelectItem>
						<SelectItem value="US">🇺🇸 United States</SelectItem>
						<SelectItem value="CA">🇨🇦 Canada</SelectItem>
						<SelectItem value="ES">🇪🇸 Spain</SelectItem>
					</SelectContent>
				</Select>
				<Select value={taxStatusFilter} onValueChange={setTaxStatusFilter}>
					<SelectTrigger className="w-[180px]">
						<Filter className="mr-2 h-4 w-4" />
						<SelectValue placeholder="Tax Status" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">All Status</SelectItem>
						<SelectItem value="rfc_provided">RFC Provided</SelectItem>
						<SelectItem value="rfc_missing">RFC Missing</SelectItem>
					</SelectContent>
				</Select>
				<Button variant="outline">
					<Download className="mr-2 h-4 w-4" />
					Export
				</Button>
			</div>

			{/* Customers Table */}
			<Card>
				<CardHeader>
					<CardTitle>Customers ({customers.length})</CardTitle>
					<CardDescription>
						All customers and their tax compliance status
					</CardDescription>
				</CardHeader>
				<CardContent>
					{/* Desktop Table View */}
					<div className="hidden md:block">
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Customer</TableHead>
									<TableHead>Country</TableHead>
									<TableHead>Licenses</TableHead>
									<TableHead>Revenue</TableHead>
									<TableHead>Tax Status</TableHead>
									<TableHead>Last Activity</TableHead>
									<TableHead className="w-[50px]">Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{customers.map((customer) => (
									<TableRow key={customer.id}>
										<TableCell>
											<div className="flex items-center space-x-3">
												<div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
													<User className="h-4 w-4 text-gray-600" />
												</div>
												<div>
													<div className="font-medium">{customer.name}</div>
													<div className="text-muted-foreground text-sm">
														{customer.email}
													</div>
												</div>
											</div>
										</TableCell>
										<TableCell>
											<div className="flex items-center space-x-2">
												<span className="text-lg">
													{getCountryFlag(customer.countryCode)}
												</span>
												<span>{getCountryName(customer.countryCode)}</span>
											</div>
										</TableCell>
										<TableCell>
											<Badge variant="outline">
												{customer.totalLicenses} licenses
											</Badge>
										</TableCell>
										<TableCell>
											{formatCurrency(customer.totalRevenue)}
										</TableCell>
										<TableCell>{getRfcBadge(customer)}</TableCell>
										<TableCell>{formatDate(customer.lastActivity)}</TableCell>
										<TableCell>
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button variant="ghost" className="h-8 w-8 p-0">
														<MoreHorizontal className="h-4 w-4" />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													<DropdownMenuLabel>Actions</DropdownMenuLabel>
													<DropdownMenuItem
														onClick={() => openCustomerDetails(customer)}
													>
														<Eye className="mr-2 h-4 w-4" />
														View Profile
													</DropdownMenuItem>
													<DropdownMenuItem>
														<Edit className="mr-2 h-4 w-4" />
														Edit Customer
													</DropdownMenuItem>
													<DropdownMenuItem>
														<Mail className="mr-2 h-4 w-4" />
														Send Email
													</DropdownMenuItem>
													<DropdownMenuSeparator />
													<DropdownMenuItem>
														<Receipt className="mr-2 h-4 w-4" />
														Generate Invoice
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										</TableCell>
									</TableRow>
								))}
							</TableBody>
						</Table>
					</div>

					{/* Mobile Card View */}
					<div className="space-y-4 md:hidden">
						{customers.map((customer) => (
							<Card key={customer.id} className="p-4">
								<div className="mb-3 flex items-start justify-between">
									<div className="flex items-center space-x-3">
										<div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
											<User className="h-4 w-4 text-gray-600" />
										</div>
										<div>
											<div className="font-medium">{customer.name}</div>
											<div className="text-muted-foreground text-sm">
												{customer.email}
											</div>
										</div>
									</div>
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="ghost" className="h-8 w-8 p-0">
												<MoreHorizontal className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuLabel>Actions</DropdownMenuLabel>
											<DropdownMenuItem
												onClick={() => openCustomerDetails(customer)}
											>
												<Eye className="mr-2 h-4 w-4" />
												View Profile
											</DropdownMenuItem>
											<DropdownMenuItem>
												<Edit className="mr-2 h-4 w-4" />
												Edit Customer
											</DropdownMenuItem>
											<DropdownMenuItem>
												<Mail className="mr-2 h-4 w-4" />
												Send Email
											</DropdownMenuItem>
											<DropdownMenuSeparator />
											<DropdownMenuItem>
												<Receipt className="mr-2 h-4 w-4" />
												Generate Invoice
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>

								<div className="space-y-3">
									<div className="flex items-center justify-between">
										<div>
											<p className="text-muted-foreground text-sm">Country</p>
											<div className="flex items-center space-x-2">
												<span className="text-lg">
													{getCountryFlag(customer.countryCode)}
												</span>
												<span>{getCountryName(customer.countryCode)}</span>
											</div>
										</div>
										<div className="text-right">
											<p className="text-muted-foreground text-sm">
												Tax Status
											</p>
											{getRfcBadge(customer)}
										</div>
									</div>

									<div className="flex items-center justify-between">
										<div>
											<p className="text-muted-foreground text-sm">Licenses</p>
											<Badge variant="outline">
												{customer.totalLicenses} licenses
											</Badge>
										</div>
										<div className="text-right">
											<p className="text-muted-foreground text-sm">Revenue</p>
											<p className="font-medium">
												{formatCurrency(customer.totalRevenue)}
											</p>
										</div>
									</div>

									<div>
										<p className="text-muted-foreground text-sm">
											Last Activity
										</p>
										<p className="text-sm">
											{formatDate(customer.lastActivity)}
										</p>
									</div>
								</div>
							</Card>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Customer Details Modal */}
			<Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
				<DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[700px]">
					<DialogHeader>
						<DialogTitle>Customer Profile</DialogTitle>
						<DialogDescription>
							Detailed customer information and tax compliance status
						</DialogDescription>
					</DialogHeader>
					{selectedCustomer && (
						<div className="grid gap-4 py-4">
							<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
								<div>
									<Label className="font-medium text-sm">Name</Label>
									<p className="mt-1">{selectedCustomer.name}</p>
								</div>
								<div>
									<Label className="font-medium text-sm">Email</Label>
									<p className="mt-1">{selectedCustomer.email}</p>
								</div>
							</div>
							<div className="grid grid-cols-2 gap-4">
								<div>
									<Label className="font-medium text-sm">Country</Label>
									<div className="mt-1 flex items-center space-x-2">
										<span className="text-lg">
											{getCountryFlag(selectedCustomer.countryCode)}
										</span>
										<span>{getCountryName(selectedCustomer.countryCode)}</span>
									</div>
								</div>
								<div>
									<Label className="font-medium text-sm">Tax Status</Label>
									<div className="mt-1">{getRfcBadge(selectedCustomer)}</div>
								</div>
							</div>
							{selectedCustomer.rfc && (
								<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
									<div>
										<Label className="font-medium text-sm">RFC Number</Label>
										<p className="mt-1 font-mono text-sm">
											{selectedCustomer.rfc}
										</p>
									</div>
									<div>
										<Label className="font-medium text-sm">Phone</Label>
										<p className="mt-1">
											{selectedCustomer.phone || "Not provided"}
										</p>
									</div>
								</div>
							)}
							{selectedCustomer.taxAddress && (
								<div>
									<Label className="font-medium text-sm">Tax Address</Label>
									<div className="mt-1 rounded-lg border p-3">
										<p>{selectedCustomer.taxAddress.street}</p>
										<p>
											{selectedCustomer.taxAddress.city},{" "}
											{selectedCustomer.taxAddress.state}{" "}
											{selectedCustomer.taxAddress.postalCode}
										</p>
										<p>{selectedCustomer.taxAddress.country}</p>
									</div>
								</div>
							)}
							<div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
								<div>
									<Label className="font-medium text-sm">Total Licenses</Label>
									<p className="mt-1 font-bold text-2xl">
										{selectedCustomer.totalLicenses}
									</p>
								</div>
								<div>
									<Label className="font-medium text-sm">Total Revenue</Label>
									<p className="mt-1 font-bold text-2xl text-green-600">
										{formatCurrency(selectedCustomer.totalRevenue)}
									</p>
								</div>
								<div>
									<Label className="font-medium text-sm">Last Activity</Label>
									<p className="mt-1">
										{formatDate(selectedCustomer.lastActivity)}
									</p>
								</div>
							</div>
						</div>
					)}
				</DialogContent>
			</Dialog>
		</div>
	);
}
