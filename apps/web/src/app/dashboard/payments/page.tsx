"use client";

import {
	Download,
	<PERSON>,
	Filter,
	MoreH<PERSON>zontal,
	RefreshC<PERSON>,
	Search,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";

interface Payment {
	id: string;
	customerEmail: string;
	customerName: string;
	amount: number;
	currency: string;
	status: "completed" | "pending" | "failed" | "refunded";
	paymentMethod: string;
	stripePaymentId: string;
	licenseType: "standard" | "extended";
	createdAt: Date;
	refundedAt?: Date;
	refundAmount?: number;
}

// Mock payment data
const mockPayments: Payment[] = [
	{
		id: "1",
		customerEmail: "<EMAIL>",
		customerName: "John Doe",
		amount: 499,
		currency: "USD",
		status: "completed",
		paymentMethod: "card",
		stripePaymentId: "pi_1234567890",
		licenseType: "standard",
		createdAt: new Date("2024-01-15T10:30:00Z"),
	},
	{
		id: "2",
		customerEmail: "<EMAIL>",
		customerName: "Jane Smith",
		amount: 999,
		currency: "USD",
		status: "completed",
		paymentMethod: "card",
		stripePaymentId: "pi_0987654321",
		licenseType: "extended",
		createdAt: new Date("2024-01-14T16:21:00Z"),
	},
	{
		id: "3",
		customerEmail: "<EMAIL>",
		customerName: "Bob Wilson",
		amount: 499,
		currency: "USD",
		status: "refunded",
		paymentMethod: "card",
		stripePaymentId: "pi_1122334455",
		licenseType: "standard",
		createdAt: new Date("2024-01-10T09:15:00Z"),
		refundedAt: new Date("2024-01-12T14:30:00Z"),
		refundAmount: 499,
	},
];

export default function PaymentsPage() {
	const [searchTerm, setSearchTerm] = useState("");
	const [statusFilter, setStatusFilter] = useState<string>("all");
	const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
	const [isDetailsOpen, setIsDetailsOpen] = useState(false);

	// Filter payments based on search and status
	const filteredPayments = mockPayments.filter((payment) => {
		const matchesSearch =
			payment.customerEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
			payment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
			payment.stripePaymentId.toLowerCase().includes(searchTerm.toLowerCase());

		const matchesStatus =
			statusFilter === "all" || payment.status === statusFilter;

		return matchesSearch && matchesStatus;
	});

	const formatCurrency = (amount: number, currency = "USD") => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency,
		}).format(amount / 100);
	};

	const formatDate = (date: Date) => {
		return new Intl.DateTimeFormat("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		}).format(date);
	};

	const getStatusBadge = (status: Payment["status"]) => {
		switch (status) {
			case "completed":
				return <Badge variant="default">Completed</Badge>;
			case "pending":
				return <Badge variant="secondary">Pending</Badge>;
			case "failed":
				return <Badge variant="destructive">Failed</Badge>;
			case "refunded":
				return <Badge variant="outline">Refunded</Badge>;
			default:
				return <Badge variant="outline">Unknown</Badge>;
		}
	};

	const openPaymentDetails = (payment: Payment) => {
		setSelectedPayment(payment);
		setIsDetailsOpen(true);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
				<div>
					<h1 className="font-bold text-3xl tracking-tight">
						Payment & Billing
					</h1>
					<p className="text-muted-foreground">
						Track payments, process refunds, and manage billing
					</p>
				</div>
				<div className="flex gap-2">
					<Button variant="outline">
						<Download className="mr-2 h-4 w-4" />
						Export
					</Button>
					<Button variant="outline">
						<RefreshCw className="mr-2 h-4 w-4" />
						Sync Stripe
					</Button>
				</div>
			</div>

			{/* Filters */}
			<div className="flex flex-col gap-4 sm:flex-row">
				<div className="relative flex-1">
					<Search className="absolute top-2.5 left-2 h-4 w-4 text-muted-foreground" />
					<Input
						placeholder="Search payments..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="pl-8"
					/>
				</div>
				<Select value={statusFilter} onValueChange={setStatusFilter}>
					<SelectTrigger className="w-[180px]">
						<Filter className="mr-2 h-4 w-4" />
						<SelectValue placeholder="Status" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">All Status</SelectItem>
						<SelectItem value="completed">Completed</SelectItem>
						<SelectItem value="pending">Pending</SelectItem>
						<SelectItem value="failed">Failed</SelectItem>
						<SelectItem value="refunded">Refunded</SelectItem>
					</SelectContent>
				</Select>
			</div>

			{/* Payments Table */}
			<Card>
				<CardHeader>
					<CardTitle>Payments ({filteredPayments.length})</CardTitle>
					<CardDescription>
						All payment transactions and their status
					</CardDescription>
				</CardHeader>
				<CardContent>
					{/* Desktop Table View */}
					<div className="hidden overflow-x-auto md:block">
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Customer</TableHead>
									<TableHead>Amount</TableHead>
									<TableHead>Status</TableHead>
									<TableHead>License Type</TableHead>
									<TableHead>Payment Method</TableHead>
									<TableHead>Date</TableHead>
									<TableHead className="w-[50px]">Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{filteredPayments.map((payment) => (
									<TableRow key={payment.id}>
										<TableCell>
											<div>
												<div className="font-medium">
													{payment.customerName}
												</div>
												<div className="text-muted-foreground text-sm">
													{payment.customerEmail}
												</div>
											</div>
										</TableCell>
										<TableCell>
											<div className="font-medium">
												{formatCurrency(payment.amount, payment.currency)}
											</div>
											{payment.status === "refunded" &&
												payment.refundAmount && (
													<div className="text-red-600 text-sm">
														-
														{formatCurrency(
															payment.refundAmount,
															payment.currency,
														)}
													</div>
												)}
										</TableCell>
										<TableCell>{getStatusBadge(payment.status)}</TableCell>
										<TableCell>
											<Badge variant="outline" className="capitalize">
												{payment.licenseType}
											</Badge>
										</TableCell>
										<TableCell className="capitalize">
											{payment.paymentMethod}
										</TableCell>
										<TableCell>{formatDate(payment.createdAt)}</TableCell>
										<TableCell>
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button variant="ghost" className="h-8 w-8 p-0">
														<MoreHorizontal className="h-4 w-4" />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													<DropdownMenuLabel>Actions</DropdownMenuLabel>
													<DropdownMenuItem
														onClick={() => openPaymentDetails(payment)}
													>
														<Eye className="mr-2 h-4 w-4" />
														View Details
													</DropdownMenuItem>
													{payment.status === "completed" && (
														<>
															<DropdownMenuSeparator />
															<DropdownMenuItem className="text-red-600">
																<RefreshCw className="mr-2 h-4 w-4" />
																Process Refund
															</DropdownMenuItem>
														</>
													)}
												</DropdownMenuContent>
											</DropdownMenu>
										</TableCell>
									</TableRow>
								))}
							</TableBody>
						</Table>
					</div>

					{/* Mobile Card View */}
					<div className="space-y-4 md:hidden">
						{filteredPayments.map((payment) => (
							<Card key={payment.id} className="p-4">
								<div className="mb-3 flex items-start justify-between">
									<div>
										<div className="font-medium">{payment.customerName}</div>
										<div className="text-muted-foreground text-sm">
											{payment.customerEmail}
										</div>
									</div>
									<div className="flex items-center space-x-2">
										{getStatusBadge(payment.status)}
										<DropdownMenu>
											<DropdownMenuTrigger asChild>
												<Button variant="ghost" className="h-8 w-8 p-0">
													<MoreHorizontal className="h-4 w-4" />
												</Button>
											</DropdownMenuTrigger>
											<DropdownMenuContent align="end">
												<DropdownMenuLabel>Actions</DropdownMenuLabel>
												<DropdownMenuItem
													onClick={() => openPaymentDetails(payment)}
												>
													<Eye className="mr-2 h-4 w-4" />
													View Details
												</DropdownMenuItem>
												{payment.status === "completed" && (
													<>
														<DropdownMenuSeparator />
														<DropdownMenuItem className="text-red-600">
															<RefreshCw className="mr-2 h-4 w-4" />
															Process Refund
														</DropdownMenuItem>
													</>
												)}
											</DropdownMenuContent>
										</DropdownMenu>
									</div>
								</div>

								<div className="space-y-3">
									<div className="flex items-center justify-between">
										<div>
											<p className="text-muted-foreground text-sm">Amount</p>
											<div className="font-medium">
												{formatCurrency(payment.amount, payment.currency)}
											</div>
											{payment.status === "refunded" &&
												payment.refundAmount && (
													<div className="text-red-600 text-sm">
														-
														{formatCurrency(
															payment.refundAmount,
															payment.currency,
														)}
													</div>
												)}
										</div>
										<div className="text-right">
											<p className="text-muted-foreground text-sm">
												License Type
											</p>
											<Badge variant="outline" className="capitalize">
												{payment.licenseType}
											</Badge>
										</div>
									</div>

									<div className="flex items-center justify-between">
										<div>
											<p className="text-muted-foreground text-sm">
												Payment Method
											</p>
											<p className="capitalize">{payment.paymentMethod}</p>
										</div>
										<div className="text-right">
											<p className="text-muted-foreground text-sm">Date</p>
											<p className="text-sm">{formatDate(payment.createdAt)}</p>
										</div>
									</div>
								</div>
							</Card>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Payment Details Modal */}
			<Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
				<DialogContent className="sm:max-w-[600px]">
					<DialogHeader>
						<DialogTitle>Payment Details</DialogTitle>
						<DialogDescription>
							Detailed information about the payment transaction
						</DialogDescription>
					</DialogHeader>
					{selectedPayment && (
						<div className="grid gap-4 py-4">
							<div className="grid grid-cols-2 gap-4">
								<div>
									<Label className="font-medium text-sm">Customer</Label>
									<p className="mt-1">{selectedPayment.customerName}</p>
									<p className="text-muted-foreground text-sm">
										{selectedPayment.customerEmail}
									</p>
								</div>
								<div>
									<Label className="font-medium text-sm">Status</Label>
									<div className="mt-1">
										{getStatusBadge(selectedPayment.status)}
									</div>
								</div>
							</div>
							<div className="grid grid-cols-2 gap-4">
								<div>
									<Label className="font-medium text-sm">Amount</Label>
									<p className="mt-1 font-bold text-2xl">
										{formatCurrency(
											selectedPayment.amount,
											selectedPayment.currency,
										)}
									</p>
								</div>
								<div>
									<Label className="font-medium text-sm">License Type</Label>
									<p className="mt-1 capitalize">
										{selectedPayment.licenseType}
									</p>
								</div>
							</div>
							<div className="grid grid-cols-2 gap-4">
								<div>
									<Label className="font-medium text-sm">Payment Method</Label>
									<p className="mt-1 capitalize">
										{selectedPayment.paymentMethod}
									</p>
								</div>
								<div>
									<Label className="font-medium text-sm">
										Stripe Payment ID
									</Label>
									<p className="mt-1 font-mono text-sm">
										{selectedPayment.stripePaymentId}
									</p>
								</div>
							</div>
							<div className="grid grid-cols-2 gap-4">
								<div>
									<Label className="font-medium text-sm">Created</Label>
									<p className="mt-1">
										{formatDate(selectedPayment.createdAt)}
									</p>
								</div>
								{selectedPayment.refundedAt && (
									<div>
										<Label className="font-medium text-sm">Refunded</Label>
										<p className="mt-1">
											{formatDate(selectedPayment.refundedAt)}
										</p>
										{selectedPayment.refundAmount && (
											<p className="text-red-600 text-sm">
												Amount:{" "}
												{formatCurrency(
													selectedPayment.refundAmount,
													selectedPayment.currency,
												)}
											</p>
										)}
									</div>
								)}
							</div>
						</div>
					)}
				</DialogContent>
			</Dialog>
		</div>
	);
}
