"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import DashboardLayout from "@/components/dashboard-layout";
import { authClient } from "@/lib/auth-client";

export default function DashboardLayoutWrapper({
	children,
}: {
	children: React.ReactNode;
}) {
	const router = useRouter();
	const { data: session, isPending } = authClient.useSession();

	useEffect(() => {
		if (!session && !isPending) {
			router.push("/login");
		}
	}, [session, isPending, router]);

	if (isPending) {
		return (
			<div className="flex min-h-screen items-center justify-center bg-gray-50">
				<div className="text-center">
					<div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-4 border-blue-500 border-t-transparent" />
					<p className="text-gray-600">Loading dashboard...</p>
				</div>
			</div>
		);
	}

	if (!session) {
		return null; // Will redirect to login
	}

	return <DashboardLayout>{children}</DashboardLayout>;
}
