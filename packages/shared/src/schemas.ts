import { z } from "zod";

/**
 * Common validation patterns and constants
 */
export const LICENSE_TYPES = ["trial", "standard", "extended"] as const;
export const USER_ROLES = [
	"SUPER_ADMIN",
	"ADMIN",
	"<PERSON>NAGE<PERSON>",
	"USER",
	"VIEWER",
] as const;
export const INVITATION_STATUSES = [
	"PENDING",
	"ACCEPTED",
	"EXPIRED",
	"REVOKED",
] as const;
export const DEVICE_ID_MIN_LENGTH = 32;
export const DEVICE_ID_MAX_LENGTH = 128;
export const LICENSE_KEY_LENGTH = 24; // 24-character alphanumeric license key
export const MAX_EMAIL_LENGTH = 254; // RFC 5321 limit
export const MAX_APP_VERSION_LENGTH = 50;
export const MAX_ADDITIONAL_DEVICES = 50;
export const MAX_NAME_LENGTH = 100;
export const MIN_NAME_LENGTH = 2;
export const INVITATION_TOKEN_LENGTH = 32;

/**
 * Device ID validation - accepts both hex strings and UUID formats for compatibility
 * This allows for gradual migration from UUID to SHA256 hex format
 */
export const deviceIdSchema = z
	.string()
	.min(DEVICE_ID_MIN_LENGTH, {
		message: `Device ID must be at least ${DEVICE_ID_MIN_LENGTH} characters long`,
	})
	.max(DEVICE_ID_MAX_LENGTH, {
		message: `Device ID must be at most ${DEVICE_ID_MAX_LENGTH} characters long`,
	})
	.refine(
		(val) => {
			// Accept pure hexadecimal strings (SHA256 format: 64 chars)
			const hexPattern = /^[a-fA-F0-9]+$/;
			// Accept UUID format (with hyphens: 36 chars)
			const uuidPattern =
				/^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/i;

			return hexPattern.test(val) || uuidPattern.test(val);
		},
		{
			message: "Device ID must be a valid hexadecimal string or UUID format",
		},
	)
	.transform((val) => {
		// Normalize UUIDs by removing hyphens and converting to lowercase
		// This ensures consistent storage regardless of input format
		return val.replace(/-/g, "").toLowerCase();
	});

/**
 * Email validation with enhanced rules
 */
export const emailSchema = z
	.string()
	.email("Please provide a valid email address")
	.min(1, { message: "Email address cannot be empty" })
	.max(MAX_EMAIL_LENGTH, {
		message: `Email address must be at most ${MAX_EMAIL_LENGTH} characters long`,
	})
	.transform((val) => val.toLowerCase().trim()); // Normalize email

/**
 * License key validation - 24-character alphanumeric key
 */
export const licenseKeySchema = z
	.string()
	.length(LICENSE_KEY_LENGTH, {
		message: `License key must be exactly ${LICENSE_KEY_LENGTH} characters long`,
	})
	.regex(/^[A-Z0-9]+$/, {
		message: "License key must contain only uppercase letters and numbers",
	});

/**
 * App version validation
 */
export const appVersionSchema = z
	.string()
	.max(MAX_APP_VERSION_LENGTH, {
		message: `App version must be at most ${MAX_APP_VERSION_LENGTH} characters long`,
	})
	.regex(/^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+)?$/, {
		message:
			"App version must follow semantic versioning (e.g., 1.0.0 or 1.0.0-beta)",
	})
	.optional();

/**
 * License type validation
 */
export const licenseTypeSchema = z.enum(LICENSE_TYPES);

/**
 * User role validation
 */
export const userRoleSchema = z.enum(USER_ROLES);

/**
 * Invitation status validation
 */
export const invitationStatusSchema = z.enum(INVITATION_STATUSES);

/**
 * User name validation
 */
export const userNameSchema = z
	.string()
	.min(MIN_NAME_LENGTH, {
		message: `Name must be at least ${MIN_NAME_LENGTH} characters long`,
	})
	.max(MAX_NAME_LENGTH, {
		message: `Name must be at most ${MAX_NAME_LENGTH} characters long`,
	})
	.regex(/^[a-zA-Z\s'-]+$/, {
		message: "Name can only contain letters, spaces, hyphens, and apostrophes",
	})
	.transform((val) => val.trim());

/**
 * Stripe Payment Intent ID validation
 */
export const stripePaymentIntentIdSchema = z
	.string()
	.regex(/^pi_[a-zA-Z0-9_]+$/, {
		message: "Invalid Stripe payment intent ID format",
	})
	.optional();

/**
 * Device metadata schema for storing device information
 * Matches the comprehensive schema used by the server
 */
export const deviceMetadataSchema = z
	.object({
		deviceName: z.string().max(100).optional(),
		deviceType: z.string().max(50).optional(),
		deviceModel: z.string().max(50).optional(),
		operatingSystem: z.string().max(50).optional(),
		architecture: z.string().max(25).optional(),
		screenResolution: z.string().max(20).optional(),
		totalMemory: z.string().max(20).optional(),
		userNickname: z.string().max(50).optional(),
		location: z.string().max(50).optional(),
		notes: z.string().max(500).optional(),
	})
	.optional();

/**
 * Schema for creating a new license
 * Device registration now happens during license validation, not creation
 */
export const createLicenseSchema = z.object({
	email: emailSchema,
	licenseType: licenseTypeSchema,
	stripePaymentIntentId: stripePaymentIntentIdSchema,
});

/**
 * Schema for validating an existing license
 */
export const validateLicenseSchema = z.object({
	licenseKey: licenseKeySchema,
	deviceId: deviceIdSchema,
	appVersion: appVersionSchema,
	deviceMetadata: deviceMetadataSchema.optional(),
});

/**
 * Schema for resending license information
 */
export const resendLicenseSchema = z.object({
	email: emailSchema,
});

/**
 * Schema for upgrading a license (adding more devices)
 */
export const upgradeLicenseSchema = z.object({
	licenseKey: licenseKeySchema,
	additionalDevices: z
		.number()
		.int({ message: "Additional devices must be a whole number" })
		.min(1, { message: "Must add at least 1 additional device" })
		.max(MAX_ADDITIONAL_DEVICES, {
			message: `Cannot add more than ${MAX_ADDITIONAL_DEVICES} devices at once`,
		}),
	stripePaymentIntentId: stripePaymentIntentIdSchema,
});

/**
 * Schema for updating device metadata
 */
export const updateDeviceMetadataSchema = z.object({
	deviceId: deviceIdSchema,
	deviceMetadata: deviceMetadataSchema,
});

/**
 * Schema for payment intent creation
 */
export const createPaymentIntentSchema = z.object({
	licenseType: z.enum(["standard", "extended"]),
	additionalDevices: z
		.number()
		.int({ message: "Additional devices must be a whole number" })
		.min(0, { message: "Additional devices cannot be negative" })
		.max(MAX_ADDITIONAL_DEVICES, {
			message: `Cannot purchase more than ${MAX_ADDITIONAL_DEVICES} additional devices`,
		})
		.default(0),
	email: emailSchema,
});

/**
 * Schema for checkout session creation
 */
export const createCheckoutSessionSchema = z.object({
	licenseType: z.enum(["standard", "extended"]),
	additionalDevices: z
		.number()
		.int({ message: "Additional devices must be a whole number" })
		.min(0, { message: "Additional devices cannot be negative" })
		.max(MAX_ADDITIONAL_DEVICES, {
			message: `Cannot purchase more than ${MAX_ADDITIONAL_DEVICES} additional devices`,
		})
		.default(0),
	email: emailSchema,
	successUrl: z
		.url({ message: "Success URL must be a valid URL" })
		.min(1, { message: "Success URL is required" }),
	cancelUrl: z
		.url({ message: "Cancel URL must be a valid URL" })
		.min(1, { message: "Cancel URL is required" }),
});

/**
 * Schema for upgrade payment intent creation
 */
export const createUpgradePaymentIntentSchema = z.object({
	licenseKey: licenseKeySchema,
	additionalDevices: z
		.number()
		.int({ message: "Additional devices must be a whole number" })
		.min(1, { message: "Must add at least 1 additional device" })
		.max(MAX_ADDITIONAL_DEVICES, {
			message: `Cannot add more than ${MAX_ADDITIONAL_DEVICES} devices at once`,
		}),
});

/**
 * Schema for requesting a refund
 */
export const requestRefundSchema = z.object({
	licenseKey: licenseKeySchema,
	reason: z
		.string()
		.min(10, { message: "Reason must be at least 10 characters long" })
		.max(500, { message: "Reason must be at most 500 characters long" }),
	requestedBy: emailSchema,
});

/**
 * Schema for processing a refund (admin endpoint)
 */
export const processRefundSchema = z.object({
	licenseKey: licenseKeySchema,
	action: z.enum(["approve", "reject"]),
	adminNotes: z
		.string()
		.max(1000, { message: "Admin notes must be at most 1000 characters long" })
		.optional(),
	amount: z
		.number()
		.int({ message: "Amount must be a whole number" })
		.positive({ message: "Amount must be a positive integer" })
		.optional(),
});

/**
 * Schema for creating a new user
 */
export const createUserSchema = z.object({
	name: userNameSchema,
	email: emailSchema,
	role: userRoleSchema.default("USER"),
});

/**
 * Schema for updating user information
 */
export const updateUserSchema = z.object({
	name: userNameSchema.optional(),
	role: userRoleSchema.optional(),
	isActive: z.boolean().optional(),
});

/**
 * Schema for sending user invitations
 */
export const sendInvitationSchema = z.object({
	email: emailSchema,
	role: userRoleSchema.default("USER"),
});

/**
 * Schema for accepting user invitations
 */
export const acceptInvitationSchema = z.object({
	token: z
		.string()
		.length(INVITATION_TOKEN_LENGTH, {
			message: `Invitation token must be exactly ${INVITATION_TOKEN_LENGTH} characters long`,
		})
		.regex(/^[a-zA-Z0-9]+$/, {
			message: "Invitation token must contain only alphanumeric characters",
		}),
	name: userNameSchema,
	password: z
		.string()
		.min(8, { message: "Password must be at least 8 characters long" })
		.regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
			message:
				"Password must contain at least one lowercase letter, one uppercase letter, and one number",
		}),
});

/**
 * Base pagination schema for reuse across endpoints
 */
export const paginationSchema = z.object({
	page: z
		.number()
		.int({ message: "Page must be a whole number" })
		.min(1, { message: "Page must be at least 1" })
		.default(1),
	limit: z
		.number()
		.int({ message: "Limit must be a whole number" })
		.min(1, { message: "Limit must be at least 1" })
		.max(100, { message: "Limit cannot exceed 100" })
		.default(20),
});

/**
 * Schema for user listing with filters
 */
export const listUsersSchema = paginationSchema.extend({
	role: userRoleSchema.optional(),
	isActive: z.boolean().optional(),
	search: z
		.string()
		.max(100, { message: "Search term cannot exceed 100 characters" })
		.optional(),
});

/**
 * Schema for license listing with filters
 */
export const listLicensesSchema = paginationSchema.extend({
	licenseType: licenseTypeSchema.optional(),
	isActive: z.boolean().optional(),
	isExpired: z.boolean().optional(),
	search: z
		.string()
		.max(100, { message: "Search term cannot exceed 100 characters" })
		.optional(),
	email: z.string().email({ message: "Invalid email format" }).optional(),
});

/**
 * Schema for refund history listing with filters
 */
export const listRefundsSchema = paginationSchema.extend({
	status: z
		.enum(["PENDING", "APPROVED", "REJECTED", "PROCESSED", "FAILED"])
		.optional(),
	search: z
		.string()
		.max(100, { message: "Search term cannot exceed 100 characters" })
		.optional(),
});

/**
 * Schema for revoking user invitations
 */
export const revokeInvitationSchema = z.object({
	invitationId: z
		.string()
		.min(1, { message: "Invitation ID is required" })
		.regex(/^[a-z0-9]+$/, { message: "Invalid invitation ID format" }),
});

/**
 * Schema for user role change requests
 */
export const changeUserRoleSchema = z.object({
	userId: z
		.string()
		.min(1, { message: "User ID is required" })
		.regex(/^[a-z0-9]+$/, { message: "Invalid user ID format" }),
	newRole: userRoleSchema,
	reason: z
		.string()
		.min(10, { message: "Reason must be at least 10 characters long" })
		.max(500, { message: "Reason must be at most 500 characters long" })
		.optional(),
});

// Export all schemas for easy importing
export const schemas = {
	createLicense: createLicenseSchema,
	validateLicense: validateLicenseSchema,
	resendLicense: resendLicenseSchema,
	upgradeLicense: upgradeLicenseSchema,
	createPaymentIntent: createPaymentIntentSchema,
	createCheckoutSession: createCheckoutSessionSchema,
	createUpgradePaymentIntent: createUpgradePaymentIntentSchema,
	requestRefund: requestRefundSchema,
	processRefund: processRefundSchema,
	deviceMetadata: deviceMetadataSchema,
	updateDeviceMetadata: updateDeviceMetadataSchema,
	// RBAC schemas
	createUser: createUserSchema,
	updateUser: updateUserSchema,
	sendInvitation: sendInvitationSchema,
	acceptInvitation: acceptInvitationSchema,
	listUsers: listUsersSchema,
	revokeInvitation: revokeInvitationSchema,
	// Pagination schemas
	pagination: paginationSchema,
	listLicenses: listLicensesSchema,
	listRefunds: listRefundsSchema,
	changeUserRole: changeUserRoleSchema,
} as const;
