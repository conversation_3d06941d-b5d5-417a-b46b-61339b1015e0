import type { z } from "zod";
import type {
	acceptInvitationSchema,
	changeUserRoleSchema,
	createCheckoutSessionSchema,
	createLicenseSchema,
	createPaymentIntentSchema,
	createUpgradePaymentIntentSchema,
	createUserSchema,
	deviceMetadataSchema,
	invitationStatusSchema,
	licenseTypeSchema,
	listLicensesSchema,
	listRefundsSchema,
	listUsersSchema,
	paginationSchema,
	processRefundSchema,
	requestRefundSchema,
	resendLicenseSchema,
	revokeInvitationSchema,
	sendInvitationSchema,
	updateDeviceMetadataSchema,
	updateUserSchema,
	upgradeLicenseSchema,
	userRoleSchema,
	validateLicenseSchema,
} from "./schemas";

// Infer types from schemas for type safety
export type LicenseType = z.infer<typeof licenseTypeSchema>;
export type DeviceMetadata = z.infer<typeof deviceMetadataSchema>;
export type UserRole = z.infer<typeof userRoleSchema>;
export type InvitationStatus = z.infer<typeof invitationStatusSchema>;

// Base pagination types
export type PaginationRequest = z.infer<typeof paginationSchema>;

// RBAC request/response types
export type CreateUserRequest = z.infer<typeof createUserSchema>;
export type UpdateUserRequest = z.infer<typeof updateUserSchema>;
export type SendInvitationRequest = z.infer<typeof sendInvitationSchema>;
export type AcceptInvitationRequest = z.infer<typeof acceptInvitationSchema>;
export type ListUsersRequest = z.infer<typeof listUsersSchema>;
export type RevokeInvitationRequest = z.infer<typeof revokeInvitationSchema>;
export type ChangeUserRoleRequest = z.infer<typeof changeUserRoleSchema>;

// License request types
export type ListLicensesRequest = z.infer<typeof listLicensesSchema>;

// Refund request types
export type ListRefundsRequest = z.infer<typeof listRefundsSchema>;

// User-related response types
export interface User {
	id: string;
	name: string;
	email: string;
	role: UserRole;
	isActive: boolean;
	emailVerified: boolean;
	image?: string;
	invitedBy?: string;
	invitedAt?: string;
	lastLoginAt?: string;
	createdAt: string;
	updatedAt: string;
}

export interface UserInvitation {
	id: string;
	email: string;
	role: UserRole;
	token: string;
	status: InvitationStatus;
	invitedById: string;
	invitedBy?: User;
	expiresAt: string;
	acceptedAt?: string;
	createdAt: string;
	updatedAt: string;
}

// Base pagination response interface
export interface PaginationMeta {
	page: number;
	limit: number;
	totalCount: number;
	totalPages: number;
	hasNextPage: boolean;
	hasPreviousPage: boolean;
}

// Generic paginated response interface
export interface PaginatedResponse<T> {
	data: T[];
	pagination: PaginationMeta;
}

export interface ListUsersResponse {
	users: User[];
	pagination: PaginationMeta;
}

export interface ListLicensesResponse {
	licenses: License[];
	pagination: PaginationMeta;
}

export interface SendInvitationResponse {
	invitation: UserInvitation;
	message: string;
}

export interface UserPermissions {
	canManageUsers: boolean;
	canManageRoles: boolean;
	canViewUsers: boolean;
	canInviteUsers: boolean;
	canDeactivateUsers: boolean;
	canViewAuditLogs: boolean;
	canManageSystem: boolean;
	assignableRoles: UserRole[];
}

// Request/Response types
export type CreateLicenseRequest = z.infer<typeof createLicenseSchema>;
export type ValidateLicenseRequest = z.infer<typeof validateLicenseSchema>;
export type ResendLicenseRequest = z.infer<typeof resendLicenseSchema>;
export type UpgradeLicenseRequest = z.infer<typeof upgradeLicenseSchema>;
export type CreatePaymentIntentRequest = z.infer<
	typeof createPaymentIntentSchema
>;
export type CreateCheckoutSessionRequest = z.infer<
	typeof createCheckoutSessionSchema
>;
export type CreateUpgradePaymentIntentRequest = z.infer<
	typeof createUpgradePaymentIntentSchema
>;
export type RequestRefundRequest = z.infer<typeof requestRefundSchema>;
export type ProcessRefundRequest = z.infer<typeof processRefundSchema>;
export type UpdateDeviceMetadataRequest = z.infer<
	typeof updateDeviceMetadataSchema
>;

// API Response types
export interface CreateCheckoutSessionResponse {
	sessionId: string;
	url: string;
	amount: number;
	licenseType: string;
}

export interface CreatePaymentIntentResponse {
	clientSecret: string;
	amount: number;
	licenseType: string;
	paymentIntentId: string;
}

export interface LicenseValidationResponse {
	success: boolean;
	license?: {
		licenseKey: string;
		licenseType: LicenseType;
		maxDevices: number;
		currentDevices: number;
		expiresAt: string | null;
		email: string;
	};
	error?: string;
}

export interface LicenseCreationResponse {
	success: boolean;
	license?: {
		licenseKey: string;
		licenseType: LicenseType;
		maxDevices: number;
		expiresAt: string | null;
		email: string;
	};
	error?: string;
}

// Pricing configuration
export interface PricingInfo {
	free: {
		price: number;
		maxDevices: number;
		duration: string;
		limitations: string;
	};
	standard: {
		price: number;
		maxDevices: number;
		duration: string;
	};
	extended: {
		price: number;
		maxDevices: number;
		duration: string;
	};
	additionalDevice: {
		price: number;
		description: string;
	};
}

// Error types
export interface APIError {
	error: string;
	code?: string;
	details?: Record<string, unknown>;
}

// Stripe-related types
export interface StripeMetadata {
	licenseType: string;
	additionalDevices: string;
	email: string;
	deviceId?: string;
	flow_type: "embedded" | "checkout" | "upgrade";
	licenseKey?: string;
}

// Checkout session details (for success page)
export interface CheckoutSessionDetails {
	sessionId: string;
	paymentStatus: string;
	customerEmail: string;
	amountTotal: number;
	currency: string;
	license: {
		licenseKey: string;
		licenseType: string;
		maxDevices: number;
		expiresAt: string | null;
		email: string;
	} | null;
	metadata: Record<string, string>;
}

// API Response types based on documentation
export interface License {
	id: string;
	licenseKey: string;
	email: string;
	licenseType: "trial" | "standard" | "extended";
	maxDevices: number;
	expiresAt: string | null;
	createdAt: string;
	updatedAt?: string;
	stripePaymentIntentId?: string;
	upgradePaymentIntentId?: string[];
	devicesUsed: number;
	devices: Device[];

	// Computed status fields (from API response)
	isActive: boolean;
	isExpired: boolean;

	// Refund information
	refundedAt?: string | null;
	refundReason?: string | null;
	refundAmount?: number | null;
	stripeRefundId?: string | null;
	refundRequest?: RefundRequest;
}

export interface Device {
	id: string;
	licenseId: string;
	deviceHash: string;
	salt: string;
	firstSeen: string;
	lastSeen: string;
	appVersion?: string;
	isActive: boolean;

	// Enhanced device metadata
	deviceName?: string;
	deviceType?: string;
	deviceModel?: string;
	operatingSystem?: string;
	architecture?: string;
	screenResolution?: string;
	totalMemory?: string;
	userNickname?: string;
	location?: string;
	notes?: string;
}

export interface RefundRequest {
	id: string;
	licenseId: string;
	status: "PENDING" | "APPROVED" | "REJECTED" | "PROCESSED" | "FAILED";
	reason: string;
	amount?: number | null;
	requestedBy: string;
	adminNotes?: string | null;
	processedBy?: string | null;
	createdAt: string;
	processedAt?: string | null;
	license: License;
}

export interface LicenseStatusResponse {
	licenseKey: string;
	licenseType: string;
	email: string;
	createdAt: string;
	expiresAt: string | null;
	maxDevices: number;
	devicesUsed: number;
	isExpired: boolean;
	isActive: boolean;
	trialDaysRemaining: number;
	devices: Device[];
}

export interface RefundStatusResponse {
	licenseKey: string;
	refunded: boolean;
	refundedAt: string | null;
	refundReason: string | null;
	refundAmount: number | null;
	refundRequest: RefundRequest | null;
}

export interface RefundHistoryResponse {
	refundRequests: RefundRequest[];
	pagination: PaginationMeta;
}

// Dashboard Analytics Types
export interface DashboardStats {
	totalLicenses: number;
	activeLicenses: number;
	totalRevenue: number;
	monthlyRevenue: number;
	totalCustomers: number;
	activeDevices: number;
	systemHealth: "healthy" | "warning" | "critical";
	pendingRefunds: number;
}

export interface RevenueChartData {
	month: string;
	amount: number;
	date: string;
}

export interface LicenseDistribution {
	standard: number;
	extended: number;
	trial: number;
}

export interface CustomerGrowthData {
	month: string;
	newCustomers: number;
	totalCustomers: number;
	date: string;
}

export interface DeviceAnalytics {
	deviceType: string;
	count: number;
	percentage: number;
}

export interface RecentActivity {
	id: string;
	type:
		| "license_created"
		| "payment_received"
		| "refund_requested"
		| "device_added"
		| "license_upgraded";
	description: string;
	timestamp: string;
	amount?: number;
	licenseKey?: string;
	email?: string;
}

export interface AnalyticsData {
	revenue: {
		total: number;
		monthly: number;
		growth: number;
		chartData: RevenueChartData[];
	};
	licenses: {
		total: number;
		monthly: number;
		growth: number;
		byType: LicenseDistribution;
	};
	customers: {
		total: number;
		monthly: number;
		growth: number;
		retention: number;
		chartData: CustomerGrowthData[];
	};
	devices: {
		total: number;
		active: number;
		analytics: DeviceAnalytics[];
	};
	refunds: {
		total: number;
		pending: number;
		processed: number;
		amount: number;
	};
}
